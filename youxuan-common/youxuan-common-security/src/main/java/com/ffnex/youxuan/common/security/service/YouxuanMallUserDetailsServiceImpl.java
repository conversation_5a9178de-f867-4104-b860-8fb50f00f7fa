package com.ffnex.youxuan.common.security.service;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ffnex.youxuan.admin.api.dto.UserDTO;
import com.ffnex.youxuan.admin.api.dto.UserInfo;
import com.ffnex.youxuan.admin.api.entity.SysUser;
import com.ffnex.youxuan.admin.api.feign.RemoteUserService;
import com.ffnex.youxuan.common.core.constant.SecurityConstants;
import com.ffnex.youxuan.common.core.exception.CheckedException;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.core.util.RetOps;
import com.ffnex.youxuan.mall.api.feign.RemoteMallService;
import com.ffnex.youxuan.mall.dto.SysUserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * 优选商城使用手机号+短信一键注册&登录
 */
@Slf4j
@RequiredArgsConstructor
public class YouxuanMallUserDetailsServiceImpl implements YouxuanUserDetailsService{
    private final UserDetailsService youxuanDefaultUserDetailsServiceImpl;

    private final RemoteUserService remoteUserService;
    private final RemoteMallService remoteMallService;
    @Override
    public UserDetails loadUserByUsername(String phone)  {

        try {
            R<UserInfo> social = remoteUserService.social(phone);
            if (RetOps.of(social).getData().isEmpty()) {
                // 用户不存在，先注册
                UserDTO userDTO = new UserDTO();
                String[] split = phone.split(StringPool.AT);
                userDTO.setUsername(split[1]);
                userDTO.setPhone(split[1]);
                userDTO.setPassword(RandomUtil.randomString(9));
                remoteUserService.registerUser(userDTO);
                social = remoteUserService.social(phone);

                // 注册成功后同步到mall用户表
                SysUser sysUser = social.getData().getSysUser();
                SysUserDTO sysUserDTO = new SysUserDTO();
                sysUserDTO.setUserId(sysUser.getUserId());
                sysUserDTO.setName(sysUser.getName());
                sysUserDTO.setAvatar(sysUser.getAvatar());
                sysUserDTO.setEmail(sysUser.getEmail());
                sysUserDTO.setPhone(sysUser.getPhone());

                remoteMallService.addUserProfileBySysUser(sysUserDTO);
            }

            return getUserDetails(RetOps.of(social).getData());
        } catch (Exception e) {
            throw new CheckedException(e);
        }
    }

    /**
     * 是否支持此客户端校验
     *
     * @param clientId  请求客户端
     * @param grantType 授权类型
     * @return true/false
     */
    @Override
    public boolean support(String clientId, String grantType) {
        return "yxmall".equals(clientId) && SecurityConstants.GRANT_MOBILE.equals(grantType);
    }

    /**
     * 排序值 默认取最大的
     *
     * @return 排序值
     */
    @Override
    public int getOrder() {
        return 100;
    }


    /**
     * 通过用户实体查询
     *
     * @param youxuanUser user
     * @return
     */
    @Override
    public UserDetails loadUserByUser(YouxuanUser youxuanUser) {
        return youxuanDefaultUserDetailsServiceImpl.loadUserByUsername(youxuanUser.getUsername());
    }
}
