<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~      Copyright (c) 2018-2025, youxuan All rights reserved.
  ~
  ~   Redistribution and use in source and binary forms, with or without
  ~   modification, are permitted provided that the following conditions are met:
  ~
  ~   Redistributions of source code must retain the above copyright notice,
  ~   this list of conditions and the following disclaimer.
  ~   Redistributions in binary form must reproduce the above copyright
  ~   notice, this list of conditions and the following disclaimer in the
  ~   documentation and/or other materials provided with the distribution.
  ~   Neither the name of the pig4cloud.com developer nor the names of its
  ~   contributors may be used to endorse or promote products derived from
  ~   this software without specific prior written permission.
  ~   Author: youxuan
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ffnex</groupId>
        <artifactId>youxuan-common</artifactId>
        <version>5.7.0</version>
    </parent>

    <artifactId>youxuan-common-security</artifactId>
    <packaging>jar</packaging>

    <description>youxuan 安全工具类</description>


    <dependencies>
        <!--工具类核心包-->
        <dependency>
            <groupId>com.ffnex</groupId>
            <artifactId>youxuan-common-core</artifactId>
        </dependency>
        <!--http 工具类-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-authorization-server</artifactId>
            <version>${spring.authorization.version}</version>
        </dependency>
        <!--feign-->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>
        <!--aop-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>
        <!--缓存依赖-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <!--upms fegin 调用相关工具类-->
        <dependency>
            <groupId>com.ffnex</groupId>
            <artifactId>youxuan-upms-api</artifactId>
        </dependency>

		<dependency>
			<groupId>com.ffnex</groupId>
			<artifactId>youxuan-app-server-api</artifactId>
		</dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ffnex</groupId>
            <artifactId>youxuan-mall-api</artifactId>
            <version>5.7.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
