<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ffnex</groupId>
        <artifactId>youxuan-common</artifactId>
        <version>5.7.0</version>
    </parent>

    <artifactId>youxuan-common-audit</artifactId>
    <packaging>jar</packaging>

    <description>youxuan 数据审计相关工具类</description>

    <dependencies>
        <!--  数据对比实现  -->
        <dependency>
            <groupId>org.javers</groupId>
            <artifactId>javers-core</artifactId>
        </dependency>
        <!--  切面依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!--  提供日志插入 API -->
        <dependency>
            <groupId>com.ffnex</groupId>
            <artifactId>youxuan-upms-api</artifactId>
        </dependency>
        <!--   获取上下文的操作用户     -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
    </dependencies>
</project>
