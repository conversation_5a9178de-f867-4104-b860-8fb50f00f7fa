package com.ffnex.youxuan.mall.controller.client;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ffnex.youxuan.admin.api.constant.SmsBizCodeEnum;
import com.ffnex.youxuan.admin.api.dto.MessageSmsDTO;
import com.ffnex.youxuan.admin.api.feign.RemoteMessageService;
import com.ffnex.youxuan.common.core.constant.CacheConstants;
import com.ffnex.youxuan.common.core.constant.SecurityConstants;
import com.ffnex.youxuan.common.core.constant.enums.LoginTypeEnum;
import com.ffnex.youxuan.common.core.exception.ErrorCodes;
import com.ffnex.youxuan.common.core.util.MsgUtils;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.security.annotation.Inner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/user")
public class UserClient {

    private final StringRedisTemplate redisTemplate;
    private final RemoteMessageService remoteMessageService;

    /**
     * 获取短信验证码
     * 因upms中会先验证用户是否存在，所以此处新创建
     * 同时用了sysUser表，因此auth认证后续loginHandler可以使用upms中的，所以loginType不需要自定义
     */
    @Inner(value = false)
    @GetMapping("smsCode")
    public R sendSmsCode(@RequestParam(value = "mobile") String mobile) {
        String codeObj = redisTemplate.opsForValue()
                .get(CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.SMS.getType()  + StringPool.AT + mobile);

        if (StrUtil.isNotBlank(codeObj)) {
            log.info("手机号验证码未过期:{}，{}", mobile, codeObj);
            return R.ok(Boolean.FALSE, MsgUtils.getMessage(ErrorCodes.SYS_APP_SMS_OFTEN));
        }

        String code = RandomUtil.randomNumbers(Integer.parseInt(SecurityConstants.CODE_SIZE));
        log.debug("手机号生成验证码成功:{},{}", mobile, code);
        redisTemplate.opsForValue()
                .set(CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.SMS.getType() + StringPool.AT + mobile, code,
                        SecurityConstants.CODE_TIME, TimeUnit.SECONDS);
        remoteMessageService.sendSms(MessageSmsDTO.builder().mobile(mobile).biz(SmsBizCodeEnum.SMS_NORMAL_CODE.getCode()).param(code).build());
        return R.ok(Boolean.TRUE);
    }



}
