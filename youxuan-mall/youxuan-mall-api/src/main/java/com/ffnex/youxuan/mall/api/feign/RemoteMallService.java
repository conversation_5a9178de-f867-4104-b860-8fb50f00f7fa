package com.ffnex.youxuan.mall.api.feign;

import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.feign.annotation.NoToken;
import com.ffnex.youxuan.mall.dto.SysUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteMallService", value = "youxuan-mall-biz")
public interface RemoteMallService {

    @NoToken
    @PostMapping("/userProfile/add_by_user")
    R<Boolean> addUserProfileBySysUser(@RequestBody SysUserDTO sysUser);
}
