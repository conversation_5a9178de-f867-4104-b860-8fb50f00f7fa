-- MySQL dump 10.13  Distrib 8.0.32, for macos13.0 (x86_64)
--
-- Host: localhost    Database: youxuan_erp
-- ------------------------------------------------------
-- Server version	8.0.32

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `account`
--

DROP TABLE IF EXISTS `account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '类型:1=银行,2=现金,3:其他',
  `account_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '户名',
  `account_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账号',
  `account_bank` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行/平台',
  `balance` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `initial_balance` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '期初余额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=停用',
  `weigh` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `tenant_id` int unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1860698379162308610 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='账户';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account`
--

LOCK TABLES `account` WRITE;
/*!40000 ALTER TABLE `account` DISABLE KEYS */;
INSERT INTO `account` VALUES (1860698001616228354,'对公账户',1,'合肥未来优选科技有限公司','*************','招商银行合肥分行',0.00,0.00,1,10,1,0,'2024-11-24 22:52:28','2024-11-24 22:52:28'),(1860698379162308609,'个人微信',3,'王xx','<EMAIL>','微信支付',0.00,0.00,1,20,1,0,'2024-11-24 22:53:58','2024-11-24 22:53:58');
/*!40000 ALTER TABLE `account` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `attribute`
--

DROP TABLE IF EXISTS `attribute`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attribute` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `attribute_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '属性编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `spu_group_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '品类',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `deleted` (`deleted`,`tenant_id`),
  KEY `spu_group_id` (`spu_group_id`),
  KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品属性';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `attribute`
--

LOCK TABLES `attribute` WRITE;
/*!40000 ALTER TABLE `attribute` DISABLE KEYS */;
/*!40000 ALTER TABLE `attribute` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `attribute_option`
--

DROP TABLE IF EXISTS `attribute_option`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attribute_option` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `attribute_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '属性',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `deleted` (`deleted`,`tenant_id`),
  KEY `attribute_id` (`attribute_id`),
  KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='属性选项';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `attribute_option`
--

LOCK TABLES `attribute_option` WRITE;
/*!40000 ALTER TABLE `attribute_option` DISABLE KEYS */;
/*!40000 ALTER TABLE `attribute_option` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `brand`
--

DROP TABLE IF EXISTS `brand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brand` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `brand_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=停用',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `brand_sn` (`brand_sn`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1860684357746622466 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='品牌';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brand`
--

LOCK TABLES `brand` WRITE;
/*!40000 ALTER TABLE `brand` DISABLE KEYS */;
INSERT INTO `brand` VALUES (1860684133682708482,'B001','蓝哲',10,1,1,0,'2024-11-24 21:57:22','2024-11-24 21:57:22'),(1860684357746622465,'B002','Z\'HUA',20,1,1,0,'2024-11-24 21:58:15','2024-11-24 21:58:15');
/*!40000 ALTER TABLE `brand` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `category`
--

DROP TABLE IF EXISTS `category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `category` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `pid` bigint unsigned NOT NULL DEFAULT '0' COMMENT '上层ID',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=停用',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `category_sn` (`category_sn`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1860674048084447234 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品分类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `category`
--

LOCK TABLES `category` WRITE;
/*!40000 ALTER TABLE `category` DISABLE KEYS */;
INSERT INTO `category` VALUES (1860673852508246018,'C001','足浴类',0,10,1,1,0,'2024-11-24 21:16:31','2024-11-24 21:22:16'),(1860673945730846721,'C002','沐浴类',0,20,1,1,0,'2024-11-24 21:16:53','2024-11-24 21:16:53'),(1860674048084447233,'C003','护肤类',0,30,1,1,0,'2024-11-24 21:17:17','2024-11-24 21:17:17');
/*!40000 ALTER TABLE `category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customer`
--

DROP TABLE IF EXISTS `customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `customer_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人',
  `sale_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '销售总额',
  `receivable_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '应收账款',
  `account_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行户名',
  `account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行账号',
  `account_bank` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户银行',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '手机号',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地区',
  `address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `create_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `deleted` (`deleted`,`tenant_id`),
  KEY `customer_sn` (`customer_sn`),
  KEY `name` (`name`),
  KEY `contact_name` (`contact_name`),
  KEY `mobile` (`mobile`)
) ENGINE=InnoDB AUTO_INCREMENT=1860932311300440066 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='客户';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer`
--

LOCK TABLES `customer` WRITE;
/*!40000 ALTER TABLE `customer` DISABLE KEYS */;
INSERT INTO `customer` VALUES (1860932311300440065,'C0001','开心沐足','孙总',0.00,0.00,'','','','手机号','','','','','',10,1,'admin',1,0,'2024-11-25 14:23:32','2024-11-25 14:23:32');
/*!40000 ALTER TABLE `customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `goods`
--

DROP TABLE IF EXISTS `goods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `goods` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `goods_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `category_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '分类',
  `brand_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '品牌',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `spu_group_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '品类',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=停用',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `goods_sn` (`goods_sn`),
  KEY `name` (`name`),
  KEY `category_id` (`category_id`),
  KEY `brand_id` (`brand_id`),
  KEY `spu_group_id` (`spu_group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1861299354801618946 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `goods`
--

LOCK TABLES `goods` WRITE;
/*!40000 ALTER TABLE `goods` DISABLE KEYS */;
INSERT INTO `goods` VALUES (1861060858568577025,'G0001','天然香薰浴盐',1860673852508246018,1860684133682708482,10,1860940259628228610,1,1,0,'2024-11-25 22:54:20','2024-11-26 13:11:37'),(1861299354801618945,'111','111',1860673852508246018,0,0,0,1,1,1,'2024-11-26 14:42:02','2024-11-26 14:42:15');
/*!40000 ALTER TABLE `goods` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `goods_attribute_value`
--

DROP TABLE IF EXISTS `goods_attribute_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `goods_attribute_value` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `goods_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品',
  `attribute_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '属性',
  `attribute_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '属性值',
  PRIMARY KEY (`id`),
  KEY `goods_id` (`goods_id`,`attribute_id`,`attribute_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品属性关联';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `goods_attribute_value`
--

LOCK TABLES `goods_attribute_value` WRITE;
/*!40000 ALTER TABLE `goods_attribute_value` DISABLE KEYS */;
/*!40000 ALTER TABLE `goods_attribute_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `goods_sku`
--

DROP TABLE IF EXISTS `goods_sku`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `goods_sku` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `goods_sku_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU编码',
  `goods_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `mnemonic` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '助记码',
  `specification` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位',
  `purchase_price` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '采购价',
  `sale_price` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '销售价',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `deleted` (`deleted`,`tenant_id`),
  KEY `goods_sku_sn` (`goods_sku_sn`),
  KEY `name` (`name`),
  KEY `mnemonic` (`mnemonic`)
) ENGINE=InnoDB AUTO_INCREMENT=1861276599947448323 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品SKU';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `goods_sku`
--

LOCK TABLES `goods_sku` WRITE;
/*!40000 ALTER TABLE `goods_sku` DISABLE KEYS */;
INSERT INTO `goods_sku` VALUES (1861060859893977090,'LZ001',1861060858568577025,'天然香薰浴盐','常规','','10g/袋','袋',3.00,8.00,0,1,0,'2024-11-25 22:54:20','2024-11-26 13:11:37'),(1861276599947448322,'LZ002',1861060858568577025,'','大包装','','15g/袋','袋',3.50,10.00,0,1,0,'2024-11-26 13:11:37','2024-11-26 13:11:37');
/*!40000 ALTER TABLE `goods_sku` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory`
--

DROP TABLE IF EXISTS `inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `goods_sku_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品SKU',
  `warehouse_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库',
  `quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '即时库存',
  `occupied_quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '锁定库存',
  `available_quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '可用库存',
  `unit_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '单位成本',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '库存总额',
  `locked_status` tinyint NOT NULL DEFAULT '0' COMMENT '锁定状态:0=未锁定,1=已锁定',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`),
  KEY `goods_sku_id` (`goods_sku_id`),
  KEY `warehouse_id` (`warehouse_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1864289065383579651 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='当前库存';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory`
--

LOCK TABLES `inventory` WRITE;
/*!40000 ALTER TABLE `inventory` DISABLE KEYS */;
INSERT INTO `inventory` VALUES (1864289065278722049,1861060859893977090,1859899373347713026,90.00,0.00,0.00,3.00,270.00,0,1,0,'2024-12-04 20:42:05','2024-12-05 14:54:55'),(1864289065383579650,1861276599947448322,1859899373347713026,190.00,0.00,0.00,3.50,665.00,0,1,0,'2024-12-04 20:42:05','2024-12-05 14:54:55');
/*!40000 ALTER TABLE `inventory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_inbound`
--

DROP TABLE IF EXISTS `inventory_inbound`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_inbound` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `inbound_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '入库单号',
  `business_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '入库类型:1=进货,2=销售退货,3=调入,4=盘盈',
  `business_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '业务ID',
  `business_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
  `warehouse_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库',
  `warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '仓库名称',
  `total_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '入库总金额',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `review_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '审核状态:0=待审核,1=审核通过',
  `inbound_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '入库状态:0=待入库,1=已入库',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=作废',
  `create_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '创建人',
  `review_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '审核人',
  `review_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '审核人',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `inbound_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '入库人',
  `inbound_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '入库人',
  `inbound_time` datetime DEFAULT NULL COMMENT '入库时间',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `business_type` (`business_type`,`business_id`),
  KEY `inbound_sn` (`inbound_sn`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `inbound_status` (`inbound_status`),
  KEY `review_status` (`review_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1864289065027063811 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='入库单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_inbound`
--

LOCK TABLES `inventory_inbound` WRITE;
/*!40000 ALTER TABLE `inventory_inbound` DISABLE KEYS */;
INSERT INTO `inventory_inbound` VALUES (1864289065027063810,'RK202412040001',1,1864289064867680258,'JH202412040001',1859899373347713026,'优选仓库',1000.00,'',1,1,1,0,'创建人',0,'审核人','2024-12-04 20:42:05',0,'入库人','2024-12-04 20:42:05',1,0,'2024-12-04 20:42:05','2024-12-05 21:59:35');
/*!40000 ALTER TABLE `inventory_inbound` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_inbound_item`
--

DROP TABLE IF EXISTS `inventory_inbound_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_inbound_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `inventory_inbound_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '入库单',
  `goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_sku_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品SKU',
  `goods_sku_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品SKU名称',
  `quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '入库数量',
  `unit_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '入库成本',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品单位',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '入库金额',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`),
  KEY `inventory_inbound_id` (`inventory_inbound_id`),
  KEY `goods_sku_id` (`goods_sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1864289065333248002 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='入库明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_inbound_item`
--

LOCK TABLES `inventory_inbound_item` WRITE;
/*!40000 ALTER TABLE `inventory_inbound_item` DISABLE KEYS */;
INSERT INTO `inventory_inbound_item` VALUES (1864289065060618241,1864289065027063810,'天然香薰浴盐',1861060859893977090,'常规',100.00,3.00,'袋',300.00,'',1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05'),(1864289065333248001,1864289065027063810,'',1861276599947448322,'大包装',200.00,3.50,'袋',700.00,'',1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05');
/*!40000 ALTER TABLE `inventory_inbound_item` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_log`
--

DROP TABLE IF EXISTS `inventory_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `business_type` tinyint NOT NULL DEFAULT '0' COMMENT '业务类型:1=入库,-1=出库',
  `business_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '业务ID',
  `business_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务单号',
  `warehouse_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库',
  `warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '仓库名称',
  `goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_sku_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品SKU名称',
  `goods_sku_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品SKU编号',
  `goods_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品单位',
  `pre_quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动前数量',
  `quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动数量',
  `post_quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动后数量',
  `pre_unit_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动前成本',
  `unit_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动成本',
  `post_unit_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动后成本',
  `pre_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动前金额',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动金额',
  `post_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '变动后金额',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`),
  KEY `business_type` (`business_type`,`business_id`),
  KEY `goods_sku_id` (`goods_sku_id`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1864564088337928195 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='库存流水';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_log`
--

LOCK TABLES `inventory_log` WRITE;
/*!40000 ALTER TABLE `inventory_log` DISABLE KEYS */;
INSERT INTO `inventory_log` VALUES (1864289065299693570,1,1864289065027063810,'RK202412040001',1859899373347713026,'优选仓库','天然香薰浴盐','常规',1861060859893977090,'袋',0.00,100.00,100.00,0.00,3.00,3.00,0.00,300.00,300.00,'',1,0,'2024-12-04 20:42:05','2024-12-05 22:00:08'),(1864289065400356865,1,1864289065027063810,'RK202412040001',1859899373347713026,'优选仓库','','大包装',1861276599947448322,'袋',0.00,200.00,200.00,0.00,3.50,3.50,0.00,700.00,700.00,'',1,0,'2024-12-04 20:42:05','2024-12-05 22:00:08'),(1864564088228876290,-1,1864564087889137666,'CK202412050002',1859899373347713026,'优选仓库','天然香薰浴盐','常规',1861060859893977090,'袋',100.00,10.00,90.00,3.00,3.00,3.00,300.00,30.00,270.00,'',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55'),(1864564088337928194,-1,1864564087889137666,'CK202412050002',1859899373347713026,'优选仓库','','大包装',1861276599947448322,'袋',200.00,10.00,190.00,3.50,3.50,3.50,700.00,35.00,665.00,'',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55');
/*!40000 ALTER TABLE `inventory_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_outbound`
--

DROP TABLE IF EXISTS `inventory_outbound`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_outbound` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `outbound_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '出库单号',
  `business_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '出库类型:1=销售,2=进货退货,3=调出,4=盘亏',
  `business_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '业务ID',
  `business_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
  `warehouse_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库',
  `warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '仓库名称',
  `total_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '总成本',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `review_status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态:0=未审核,1=审核通过',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=作废',
  `create_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `review_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID',
  `review_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核人',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `outbound_status` tinyint NOT NULL DEFAULT '0' COMMENT '出库状态:0=未出库,1=已出库',
  `outbound_time` datetime DEFAULT NULL COMMENT '出库时间',
  `outbound_user` bigint unsigned NOT NULL DEFAULT '0' COMMENT '出库人',
  `outbound_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '出库人',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `business_type` (`business_type`,`business_id`),
  KEY `review_status` (`review_status`),
  KEY `outbound_status` (`outbound_status`),
  KEY `outbound_sn` (`outbound_sn`)
) ENGINE=InnoDB AUTO_INCREMENT=1864564087889137667 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='出库单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_outbound`
--

LOCK TABLES `inventory_outbound` WRITE;
/*!40000 ALTER TABLE `inventory_outbound` DISABLE KEYS */;
INSERT INTO `inventory_outbound` VALUES (1864564087889137666,'CK202412050002',1,1864564087788474370,'XS202412050003',1859899373347713026,'优选仓库',65.00,'',1,1,1,'admin',1,'admin','2024-12-05 14:54:55',1,'2024-12-05 14:54:55',1,'admin',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55');
/*!40000 ALTER TABLE `inventory_outbound` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_outbound_item`
--

DROP TABLE IF EXISTS `inventory_outbound_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_outbound_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `inventory_outbound_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '出库单',
  `goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_sku_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品SKU',
  `goods_sku_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品SKU名称',
  `quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '出库数量',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位',
  `unit_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '单位成本',
  `cost_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '成本总额',
  `calc_sale_price` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '销售单价',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`),
  KEY `inventory_outbound_id` (`inventory_outbound_id`),
  KEY `goods_sku_id` (`goods_sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1864564088270819331 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='出库明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_outbound_item`
--

LOCK TABLES `inventory_outbound_item` WRITE;
/*!40000 ALTER TABLE `inventory_outbound_item` DISABLE KEYS */;
INSERT INTO `inventory_outbound_item` VALUES (1864564087960440834,1864564087889137666,'天然香薰浴盐',1861060859893977090,'常规',10.00,'袋',3.00,30.00,8.00,'',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55'),(1864564088270819330,1864564087889137666,'',1861276599947448322,'大包装',10.00,'袋',3.50,35.00,10.00,'',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55');
/*!40000 ALTER TABLE `inventory_outbound_item` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_order`
--

DROP TABLE IF EXISTS `purchase_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '进货单号',
  `warehouse_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库',
  `warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '仓库名称',
  `supplier_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `supplier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '供应商',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `review_status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态:0=未审核,1=审核通过',
  `inbound_status` tinyint NOT NULL DEFAULT '0' COMMENT '入库状态:0=未入库,1=已入库,2=部分入库',
  `total_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
  `purchase_date` date NOT NULL DEFAULT '1111-11-11' COMMENT '进货日期',
  `create_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
  `review_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '审核人',
  `review_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核人名称',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:1=正常,2=作废',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `order_sn` (`order_sn`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `supplier_id` (`supplier_id`),
  KEY `purchase_date` (`purchase_date`),
  KEY `review_status` (`review_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1864289065572323331 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='进货订单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_order`
--

LOCK TABLES `purchase_order` WRITE;
/*!40000 ALTER TABLE `purchase_order` DISABLE KEYS */;
INSERT INTO `purchase_order` VALUES (1861670117685682177,'',1859899373347713026,'优选仓库',1860919055634706434,'黑泥大盐工厂','d',1,0,0.00,'2024-11-26',0,'',1,'管理员','2024-11-29 13:35:55',1,1,0,'2024-11-27 15:15:19','2024-11-29 13:35:54'),(1861721535654178818,'JH202411270001',1859899373347713026,'优选仓库',1860919055634706434,'黑泥大盐工厂','444',0,0,235.00,'2024-11-26',0,'',0,'',NULL,1,1,0,'2024-11-27 18:39:38','2024-11-29 13:35:25'),(1864289064867680258,'PURCHASE_ORDER202412040001',1859899373347713026,'优选仓库',1860919055634706434,'黑泥大盐工厂','w',1,0,1000.00,'2024-12-01',0,'',1,'管理员','2024-12-04 20:42:04',1,1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05'),(1864289065572323330,'PURCHASE_ORDER202412040002',1859899373347713026,'优选仓库',1860919055634706434,'黑泥大盐工厂','w',0,0,1000.00,'2024-12-01',0,'',0,'',NULL,1,1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05');
/*!40000 ALTER TABLE `purchase_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_order_item`
--

DROP TABLE IF EXISTS `purchase_order_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_order_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `purchase_order_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '进货订单',
  `goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_sku_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品SKU',
  `goods_sku_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品SKU名称',
  `quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '数量',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位',
  `unit_cost` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '单位成本',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '进货金额',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`),
  KEY `purchase_order_id` (`purchase_order_id`),
  KEY `goods_sku_id` (`goods_sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1877570984140615682 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='进货明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_order_item`
--

LOCK TABLES `purchase_order_item` WRITE;
/*!40000 ALTER TABLE `purchase_order_item` DISABLE KEYS */;
INSERT INTO `purchase_order_item` VALUES (1861670117786345473,1861670117685682177,'天然香薰浴盐',1861060859893977090,'常规',10.00,'',3.00,30.00,'s',1,0,'2024-11-27 15:15:19','2024-11-27 15:26:00'),(1861670117807316993,1861670117685682177,'',1861276599947448322,'大包装',5.00,'',3.50,17.50,'s',1,0,'2024-11-27 15:15:19','2024-11-27 15:26:00'),(1861783239561842689,1861721535654178818,'天然香薰浴盐',1861060859893977090,'常规',20.00,'袋',3.00,60.00,'',1,0,'2024-11-27 22:44:49','2024-11-27 22:44:49'),(1861783239595397122,1861721535654178818,'',1861276599947448322,'大包装',50.00,'袋',3.50,175.00,'',1,0,'2024-11-27 22:44:49','2024-11-27 22:44:49'),(1862035241537671169,0,'',0,'',0.00,'',0.00,0.00,'',1,0,'2024-11-28 15:26:11','2024-11-28 15:26:11'),(1862039602129047554,0,'',0,'',0.00,'',0.00,0.00,'',1,0,'2024-11-28 15:43:31','2024-11-28 15:43:31'),(1864289064968343554,1864289064867680258,'天然香薰浴盐',1861060859893977090,'常规',100.00,'袋',3.00,300.00,'30',1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05'),(1864289064985120770,1864289064867680258,'',1861276599947448322,'大包装',200.00,'袋',3.50,700.00,'7',1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05'),(1864289065584906241,1864289065572323330,'天然香薰浴盐',1861060859893977090,'常规',100.00,'袋',3.00,300.00,'30',1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05'),(1864289065597489153,1864289065572323330,'',1861276599947448322,'大包装',200.00,'袋',3.50,700.00,'7',1,0,'2024-12-04 20:42:05','2024-12-04 20:42:05'),(1877570984140615681,0,'',0,'',0.00,'',0.00,0.00,'',1,0,'2025-01-10 12:19:41','2025-01-10 12:19:41');
/*!40000 ALTER TABLE `purchase_order_item` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sale_order`
--

DROP TABLE IF EXISTS `sale_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sale_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '销售单号',
  `customer_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '客户',
  `warehouse_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '仓库',
  `warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '仓库名称',
  `customer_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户名称',
  `total_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
  `discount_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `change_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '抹零金额',
  `pay_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '已付款额',
  `outbound_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '出库状态:0=未出库,1=已出库,2=部分出库',
  `review_status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态:0=未审核,1=已审核',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态:1=正常,2=终止',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `sale_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '销售员ID',
  `sale_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '销售员',
  `create_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '开单人ID',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开单人',
  `review_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID',
  `review_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核人',
  `sale_date` date NOT NULL DEFAULT '1111-11-11' COMMENT '销售日期',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `customer_id` (`customer_id`),
  KEY `outbound_status` (`outbound_status`),
  KEY `review_status` (`review_status`),
  KEY `sale_user_id` (`sale_user_id`),
  KEY `sale_date` (`sale_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1864564087788474371 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='销售订单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sale_order`
--

LOCK TABLES `sale_order` WRITE;
/*!40000 ALTER TABLE `sale_order` DISABLE KEYS */;
INSERT INTO `sale_order` VALUES (1864564087788474370,'XS202412050003',1860932311300440065,1859899373347713026,'优选仓库','开心沐足',150.00,30.00,0.00,0.00,0,1,1,'x',0,'',1,'admin',1,'管理员','2024-12-05','2024-12-05 14:54:55',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55');
/*!40000 ALTER TABLE `sale_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sale_order_item`
--

DROP TABLE IF EXISTS `sale_order_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sale_order_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `sale_order_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '销货单',
  `goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_sku_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品SKU',
  `goods_sku_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品SKU名称',
  `sale_price` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '销售单价',
  `calc_price` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '核算单价',
  `quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '数量',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`),
  KEY `sale_order_id` (`sale_order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1864564087863971843 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='销售明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sale_order_item`
--

LOCK TABLES `sale_order_item` WRITE;
/*!40000 ALTER TABLE `sale_order_item` DISABLE KEYS */;
INSERT INTO `sale_order_item` VALUES (1864564087847194626,1864564087788474370,'天然香薰浴盐',1861060859893977090,'常规',8.00,8.00,10.00,'袋',80.00,'s',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55'),(1864564087863971842,1864564087788474370,'',1861276599947448322,'大包装',10.00,10.00,10.00,'袋',100.00,'s',1,0,'2024-12-05 14:54:55','2024-12-05 14:54:55');
/*!40000 ALTER TABLE `sale_order_item` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `spu_group`
--

DROP TABLE IF EXISTS `spu_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spu_group` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `spu_group_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品类编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=停用',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1860940259628228611 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品品类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `spu_group`
--

LOCK TABLES `spu_group` WRITE;
/*!40000 ALTER TABLE `spu_group` DISABLE KEYS */;
INSERT INTO `spu_group` VALUES (1860940259628228610,'A001','足浴盐',10,1,1,0,'2024-11-25 14:55:07','2024-11-25 14:55:07');
/*!40000 ALTER TABLE `spu_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `supplier`
--

DROP TABLE IF EXISTS `supplier`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supplier` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `payable_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '应付账款',
  `purchase_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '采购总额',
  `payment_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '付款总额',
  `account_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行户名',
  `account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行账号',
  `account_bank` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户银行',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '区县',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=停用',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`),
  KEY `name` (`name`),
  KEY `contact_name` (`contact_name`),
  KEY `mobile` (`mobile`)
) ENGINE=InnoDB AUTO_INCREMENT=1860924330643075075 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='供应商';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `supplier`
--

LOCK TABLES `supplier` WRITE;
/*!40000 ALTER TABLE `supplier` DISABLE KEYS */;
INSERT INTO `supplier` VALUES (1860919055634706434,'黑泥大盐工厂','李经理','13888588855',0.00,0.00,0.00,'','','','','','','','',0,1,1,0,'2024-11-25 13:30:52','2024-11-25 13:30:52'),(1860924330643075074,'蓝哲工厂','孙总','1883225252',0.00,0.00,0.00,'','','','','','','','',0,1,1,0,'2024-11-25 13:51:49','2024-11-25 13:51:49');
/*!40000 ALTER TABLE `supplier` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `warehouse`
--

DROP TABLE IF EXISTS `warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `warehouse` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `warehouse_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '仓库编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `inventory_quantity` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '库存总量',
  `inventory_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '库存总额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=启用,2=停用',
  `weigh` int NOT NULL DEFAULT '0' COMMENT '排序',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '租户',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '软删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`,`deleted`,`status`),
  KEY `warehouse_sn` (`warehouse_sn`)
) ENGINE=InnoDB AUTO_INCREMENT=1860543238975066115 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='仓库';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `warehouse`
--

LOCK TABLES `warehouse` WRITE;
/*!40000 ALTER TABLE `warehouse` DISABLE KEYS */;
INSERT INTO `warehouse` VALUES (1859899373347713026,'202401','优选仓库','备注1',0.00,0.00,1,0,1,0,'2024-11-22 17:59:01','2024-11-22 17:59:01'),(1860543238975066114,'202402','临时仓库','',0.00,0.00,2,2,1,0,'2024-11-24 12:37:30','2024-11-24 12:37:30');
/*!40000 ALTER TABLE `warehouse` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-01-12 22:05:18
