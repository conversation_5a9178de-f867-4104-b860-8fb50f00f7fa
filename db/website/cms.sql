drop table if exists product;
create table product
(
    id             bigint unsigned auto_increment primary key not null comment 'ID',
    name           varchar(255)                               not null comment '标题',
    brand_id       bigint unsigned                            not null default 0 comment '品牌',
    series_id      bigint unsigned                            not null default 0 comment '系列',
    category_id    bigint unsigned                            not null default 0 comment '产品分类',
    image          varchar(255)                               not null comment '图片',
    content        longtext                                   not null comment '内容',
    create_user_id bigint unsigned                            not null default 0 comment '创建人',
    recommended    tinyint                                    not null default 0 comment '分类推荐',
    nav_show       tinyint                                    not null default 0 comment '导航显示',
    status         tinyint unsigned                           not null default 1 comment '状态:1=正常,2=禁用',
    tenant_id      bigint unsigned                            not null default 1 comment '租户',
    create_time    datetime                                            default current_timestamp comment '创建时间',
    update_time    datetime                                            default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='产品';

drop table if exists product_series;
create table product_series
(
    id           bigint unsigned auto_increment primary key                    not null comment 'ID',
    name         varchar(255)                                                  not null comment '名称',
    cover_image  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '封面',
    banner_image varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '横幅',
    weigh        int unsigned                                                  not null default 0 comment '权重',
    tenant_id    bigint unsigned                                               not null default 1 comment '租户',
    create_time  datetime                                                               default current_timestamp comment '创建时间',
    update_time  datetime                                                               default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='系列';

drop table if exists product_brand;
create table product_brand
(
    id           bigint unsigned auto_increment primary key not null comment 'ID',
    name         varchar(255)                               not null comment '名称',
    logo_image   varchar(255)                               not null default '' comment 'logo',
    banner_image varchar(255)                               not null default '' comment '横幅',
    weigh        int unsigned                               not null default 0 comment '权重',
    tenant_id    bigint unsigned                            not null default 1 comment '租户',
    create_time  datetime                                            default current_timestamp comment '创建时间',
    update_time  datetime                                            default null on update current_timestamp comment '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='品牌';

drop table if exists product_category;
create table product_category
(
    id           bigint unsigned auto_increment primary key                    not null comment 'ID',
    name         varchar(255)                                                  not null comment '名称',
    cover_image  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '封面',
    banner_image varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '横幅',
    weigh        int unsigned                                                  not null default 0 comment '权重',
    tenant_id    bigint unsigned                                               not null default 1 comment '租户',
    create_time  datetime                                                               default current_timestamp comment '创建时间',
    update_time  datetime                                                               default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='产品分类';

drop table if exists project_manual;
create table project_manual
(
    id             bigint unsigned auto_increment primary key not null comment 'ID',
    name           varchar(255)                               not null comment '标题',
    category_id    bigint unsigned                            not null default 0 comment '项目分类',
    image          varchar(255)                               not null comment '图片',
    content        longtext                                   not null comment '内容',
    create_user_id bigint unsigned                            not null default 0 comment '创建人',
    status         tinyint unsigned                           not null default 1 comment '状态:1=正常,2=禁用',
    tenant_id      bigint unsigned                            not null default 1 comment '租户',
    create_time    datetime                                            default current_timestamp comment '创建时间',
    update_time    datetime                                            default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='项目手册';


drop table if exists project_category;
create table project_category
(
    id           bigint unsigned auto_increment primary key                    not null comment 'ID',
    name         varchar(255)                                                  not null comment '名称',
    cover_image  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '封面',
    banner_image varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '横幅',
    weigh        int unsigned                                                  not null default 0 comment '权重',
    tenant_id    bigint unsigned                                               not null default 1 comment '租户',
    create_time  datetime                                                               default current_timestamp comment '创建时间',
    update_time  datetime                                                               default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='项目分类';

drop table if exists site_config;
create table site_config
(
    id          bigint unsigned auto_increment primary key                   not null comment 'ID',
    name        varchar(255)                                                 not null comment '名称',
    field_key   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '调用标识',
    field_type  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '类型',
    field_value text                                                         null comment '内容',
    tenant_id   bigint unsigned                                              not null default 1 comment '租户',
    create_time datetime                                                     not null default current_timestamp comment '创建时间',
    update_time datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    delete_time datetime                                                     null comment '删除时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='站点设置';

drop table if exists banner;
create table banner
(
    id          bigint unsigned auto_increment primary key                   not null comment 'ID',
    name        varchar(255)                                                 not null comment '名称',
    image       varchar(255)                                                 not null comment '图片',
    position    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '位置',
    url         varchar(255)                                                 not null default '' comment '链接',
    weigh       int unsigned                                                 not null default 0 comment '权重',
    tenant_id   bigint unsigned                                              not null default 1 comment '租户',
    create_time datetime                                                     not null default current_timestamp comment '创建时间',
    update_time datetime                                                              default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='站点横幅';

drop table if exists guestbook;
create table guestbook
(
    id             bigint unsigned auto_increment primary key not null comment 'ID',
    source_type    tinyint unsigned                           not null default 0 comment '留言来源',
    name           varchar(50)                                not null default '' comment '姓名',
    phone          varchar(20)                                not null default '' comment '电话',
    email          varchar(50)                                not null default '' comment '邮箱',
    content        text                                       null comment '留言内容',
    process_status tinyint unsigned                           not null default 0 comment '状态:0=未处理,1=已处理',
    create_time    datetime                                   not null default current_timestamp comment '留言时间',
    process_time   datetime                                   null comment '处理时间',
    tenant_id      bigint unsigned                            not null default 1 comment '租户'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='站点留言';


drop table if exists qr_code;
create table qr_code
(
    id          bigint unsigned auto_increment primary key not null comment 'ID',
    name        varchar(255)                               not null comment '名称',
    code        varchar(255)                               not null comment '识别码',
    type        tinyint unsigned                           not null default 1 comment '类型:1=跳转链接,2=富文本内容',
    url         varchar(255)                               not null default '' comment '跳转链接',
    content     text                                       null comment '富文本内容',
    status      tinyint unsigned                           not null default 1 comment '状态:1=正常,2=禁用',
    tenant_id   bigint unsigned                            not null default 1 comment '租户',
    create_time datetime                                   not null default current_timestamp comment '创建时间',
    update_time datetime                                            default null on update current_timestamp COMMENT '更新时间',
    unique code_unique (tenant_id, code)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='动态二维码';