create database youxuan_mall default charset utf8mb4 collate utf8mb4_general_ci;
use youxuan_mall;
-- 站点设置内容
drop table if exists banner;
create table banner
(
    id          bigint unsigned auto_increment primary key                    not null comment 'ID',
    image       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '图片',
    bg_color    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '背景色',
    type        tinyint unsigned                                              not null default 1 comment '类型:1=首页,2=分类',
    url         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '跳转链接',
    weigh       int unsigned                                                  not null default 0 comment '排序',
    remark      varchar(255) CHARACTER SET utf8mb4 collate utf8mb4_general_ci not null default '' comment '备注',
    status      tinyint unsigned                                              not null default 1 comment '状态:1=启用,2=停用',
    tenant_id   bigint unsigned                                               not null default 1 comment '租户',
    create_time datetime                                                               default current_timestamp comment '创建时间',
    update_time datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='轮播图';

drop table if exists article;
create table article
(
    id          bigint unsigned auto_increment primary key                    not null comment 'ID',
    name        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '标题',
    content     text comment '内容',
    status      tinyint unsigned                                              not null default 1 comment '状态:1=启用,2=停用',
    tenant_id   bigint unsigned                                               not null default 1 comment '租户',
    create_time datetime                                                               default current_timestamp comment '创建时间',
    update_time datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='文章';


-- 商品模块

drop table if exists goods;
create table goods
(
    id                      bigint unsigned                                               not null primary key auto_increment,
    name                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品标题',
    category_id             bigint unsigned                                               not null default 0 comment '分类',
    original_price          decimal(10, 2)                                                not null default 0.00 comment '参考价',
    actual_price            decimal(10, 2)                                                not null default 0.00 comment '实际价',
    distribution_commission decimal(10, 2)                                                not null default 0.00 comment '佣金',
    content                 text comment '商品详情',
    image                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品主图',
    image_list              text comment '商品轮播图',
    use_credit              tinyint                                                       not null default 0 comment '是否支持积分',
    status                  tinyint                                                       not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    weigh                   int                                                           not null default 0 comment '排序',
    tenant_id               bigint unsigned                                               not null default 1 comment '租户',
    deleted                 tinyint                                                       not null default 0 comment '软删除',
    create_time             datetime                                                               default current_timestamp comment '创建时间',
    update_time             datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, status, deleted, weigh),
    index (tenant_id, category_id, weigh),
    index (tenant_id, name),                                                                                                               -- 用于商品搜索
    index (tenant_id, use_credit, weigh)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商城商品';

drop table if exists goods_sku;
create table goods_sku
(
    id                      bigint unsigned                                               not null primary key auto_increment,
    goods_id                bigint unsigned                                               not null default 0 comment '商品ID',
    name                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment 'sku名称',
    image                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment 'sku图片',
    original_price          decimal(10, 2)                                                not null default 0.00 comment '参考价',
    actual_price            decimal(10, 2)                                                not null default 0.00 comment '实际价',
    distribution_commission decimal(10, 2)                                                not null default 0.00 comment '佣金',
    credit_money_rate       decimal(10, 2)                                                not null default 0.00 comment '积分兑换价格比',
    credit_limit            decimal(10, 2)                                                not null default 0 comment '积分使用上限',
    sales                   int                                                           not null default 0 comment '销量',
    stock                   int                                                           not null default 0 comment '库存',
    weigh                   int                                                           not null default 0 comment '排序',
    tenant_id               bigint unsigned                                               not null default 1 comment '租户',
    deleted                 tinyint                                                       not null default 0 comment '软删除',
    create_time             datetime                                                               default current_timestamp comment '创建时间',
    update_time             datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, goods_id, deleted, weigh)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品SKU';

-- 商品服务标签
drop table if exists service_tag;
create table service_tag
(
    id          bigint unsigned                                               not null primary key auto_increment,
    name        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '标签名称',
    description varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '标签描述',
    status      tinyint                                                       not null default 1 comment '状态:1=启用,2=停用',
    tenant_id   bigint unsigned                                               not null default 1 comment '租户',
    create_time datetime                                                               default current_timestamp comment '创建时间',
    update_time datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='服务标签';

drop table if exists goods_service_tag;
create table goods_service_tag
(
    id             bigint unsigned not null primary key auto_increment,
    goods_id       bigint unsigned not null default 0 comment '商品ID',
    service_tag_id bigint unsigned not null default 0 comment '服务标签ID',
    weigh          int             not null default 0 comment '排序',
    tenant_id      bigint unsigned not null default 1 comment '租户',
    create_time    datetime                 default current_timestamp comment '创建时间',
    index (tenant_id, goods_id, service_tag_id, weigh)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品服务';


drop table if exists goods_recommendation_position;
create table goods_recommendation_position
(
    id          bigint unsigned auto_increment primary key                    not null comment 'ID',
    name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '名称',
    code        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '编码',
    image       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '图片',
    goods_count int unsigned                                                  not null default 0 comment '商品数量',
    description varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '描述',
    status      TINYINT                                                                DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    tenant_id   bigint unsigned                                               not null default 1 comment '租户',
    create_time datetime                                                               default current_timestamp comment '创建时间',
    update_time datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, code, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品推荐位';

drop table if exists goods_recommendation;
create table goods_recommendation
(
    id          bigint unsigned auto_increment primary key not null comment 'ID',
    goods_id    bigint unsigned                            not null default 0 comment '商品ID',
    position_id bigint unsigned                            not null default 0 comment '推荐位',
    tenant_id   bigint unsigned                            not null default 1 comment '租户',
    create_time datetime                                            default current_timestamp comment '加入时间',
    index (tenant_id, goods_id, position_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品推荐';

# 品类决定商品的属性集合
create table if not exists goods_spu_group
(
    id          bigint unsigned auto_increment primary key                   not null comment '编码',
    name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    weigh       int                                                          not null default 0 comment '排序',
    status      tinyint                                                      not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    tenant_id   bigint unsigned                                              not null default 1 comment '租户',
    deleted     tinyint                                                      not null default 0 comment '软删除',
    create_time datetime                                                              default current_timestamp comment '创建时间',
    update_time datetime                                                              default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品品类';

-- 规格名称表
CREATE TABLE goods_spu
(
    id                 BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    goods_spu_group_id BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '商品品类ID',
    name               VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '规格名称',
    status             TINYINT                                    NOT NULL DEFAULT 1 COMMENT '状态:1=启用,2=停用',
    weigh              INT                                        NOT NULL DEFAULT 0 COMMENT '排序',
    tenant_id          BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time        DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time        DATETIME                                            DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX (tenant_id, status, weigh)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='商品规格';

-- 规格值表
CREATE TABLE goods_spu_option
(
    id           BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    goods_spu_id BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '规格ID',
    value        VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '规格值',
    status       TINYINT                                    NOT NULL DEFAULT 1 COMMENT '状态:1=启用,2=停用',
    weigh        INT                                        NOT NULL DEFAULT 0 COMMENT '排序',
    tenant_id    BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time  DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time  DATETIME                                            DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX (tenant_id, goods_spu_id, status, weigh)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='规格选项';

-- 商品规格关联表
CREATE TABLE goods_spu_value
(
    id          BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    goods_id    BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '商品ID',
    spu_id      BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '规格ID',
    spu_value   BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '规格值',
    weigh       INT                                        NOT NULL DEFAULT 0 COMMENT '排序',
    tenant_id   BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX (tenant_id, goods_id, weigh)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='商品规格值';

-- 分类表
drop table if exists category;
create table category
(
    id          bigint unsigned                                               not null primary key auto_increment,
    name        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '分类名称',
    image       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '分类图片',
    parent_id   bigint unsigned                                               not null default 0 comment '上级分类',
    status      tinyint                                                       not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    weigh       int                                                           not null default 0 comment '排序',
    tenant_id   bigint unsigned                                               not null default 1 comment '租户',
    deleted     tinyint                                                       not null default 0 comment '软删除',
    create_time datetime                                                               default current_timestamp comment '创建时间',
    update_time datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, status, weigh)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品分类';

-- 收藏
drop table if exists favorite;
create table favorite
(
    id          bigint unsigned not null primary key auto_increment,
    user_id     bigint unsigned not null default 0 comment '用户ID',
    goods_id    bigint unsigned not null default 0 comment '商品ID',
    tenant_id   bigint unsigned not null default 1 comment '租户',
    create_time datetime                 default current_timestamp comment '收藏时间',
    index (tenant_id, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品收藏';

-- 足迹
drop table if exists footprint;
create table footprint
(
    id          bigint unsigned not null primary key auto_increment,
    user_id     bigint unsigned not null default 0 comment '用户ID',
    goods_id    bigint unsigned not null default 0 comment '商品ID',
    tenant_id   bigint unsigned not null default 1 comment '租户',
    create_time datetime                 default current_timestamp comment '创建时间',
    index (tenant_id, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户足迹';

-- 购物车
drop table if exists cart;
create table cart
(
    id           bigint unsigned not null primary key auto_increment,
    user_id      bigint unsigned not null default 0 comment '用户ID',
    goods_sku_id bigint unsigned not null default 0 comment '商品SKU',
    quantity     int             not null default 1 comment '数量',
    tenant_id    bigint unsigned not null default 1 comment '租户',
    create_time  datetime                 default current_timestamp comment '创建时间',
    update_time  datetime                 default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='购物车';


-- 订单模块 begin
drop table if exists order_info;
create table order_info
(
    id             bigint unsigned                                               not null primary key auto_increment,
    user_id        bigint unsigned                                               not null default 0 comment '用户ID',
    order_no       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '订单编号',
    consignee      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '收货人',
    telephone      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '收货人电话',
    province       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '省份',
    city           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '城市',
    district       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '地区',
    address        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '详细地址',
    order_remark   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '订单备注',
    status         tinyint                                                       not null default 1 comment '订单状态:1=待付款,2=待发货,3=待收货,4=已完成,5=已取消',
    comment_status tinyint                                                       not null default 0 comment '评价状态:0=未评价,1=已评价',
    total_price    decimal(10, 2)                                                not null default 0.00 comment '订单总金额',
    pay_price      decimal(10, 2)                                                not null default 0.00 comment '实际支付金额',
    pay_time       datetime                                                               default null comment '支付时间',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    deleted        tinyint                                                       not null default 0 comment '软删除',
    create_time    datetime                                                               default current_timestamp comment '创建时间',
    update_time    datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id, deleted, status),
    index (tenant_id, order_no),
    index (tenant_id, create_time),
    index (tenant_id, pay_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='订单';

drop table if exists order_item;
create table order_item
(
    id             bigint unsigned                                               not null primary key auto_increment,
    order_id       bigint unsigned                                               not null default 0 comment '订单ID',
    goods_id       bigint unsigned                                               not null default 0 comment '商品ID',    # 冗余
    goods_name     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品名称', # 冗余
    goods_image    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品图片', # 冗余
    goods_sku_id   bigint unsigned                                               not null default 0 comment '商品SKU',
    goods_sku_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品SKU名称',
    quantity       int                                                           not null default 0 comment '购买数量',
    price          decimal(10, 2)                                                not null default 0.00 comment '商品单价',
    total_price    decimal(10, 2)                                                not null default 0.00 comment '商品总价',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    deleted        tinyint                                                       not null default 0 comment '软删除',
    create_time    datetime                                                               default current_timestamp comment '创建时间',
    update_time    datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, order_id, deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='订单项';

# 订单评价
drop table if exists order_comment;
create table order_comment
(
    id            bigint unsigned                                               not null primary key auto_increment,
    user_id       bigint unsigned                                               not null default 0 comment '用户ID',
    order_id      bigint unsigned                                               not null default 0 comment '订单ID',
    goods_id      bigint unsigned                                               not null default 0 comment '商品ID',
    content       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '评价内容',
    score         tinyint                                                       not null default 0 comment '评分',
    images        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '图片',
    reply_content varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '回复内容',
    status        tinyint                                                       not null default 0 comment '状态:0=未显示,1=已显示',
    tenant_id     bigint unsigned                                               not null default 1 comment '租户',
    deleted       tinyint                                                       not null default 0 comment '软删除',
    create_time   datetime                                                               default current_timestamp comment '创建时间',
    update_time   datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id, deleted),
    index (tenant_id, order_id, deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='订单评价';
-- 订单模块 end


-- 地址
drop table if exists address;
create table address
(
    id           bigint unsigned                                               not null primary key auto_increment,
    user_id      bigint unsigned                                               not null default 0 comment '用户ID',
    name         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '地址名',
    consignee    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '收货人',
    telephone    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '收货人电话',
    province     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '省份',
    city         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '城市',
    district     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '地区',
    address      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '详细地址',
    default_flag tinyint                                                       not null default 0 comment '默认地址',
    tenant_id    bigint unsigned                                               not null default 1 comment '租户',
    deleted      tinyint                                                       not null default 0 comment '软删除',
    create_time  datetime                                                               default current_timestamp comment '创建时间',
    update_time  datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id, deleted, default_flag)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='收货地址';

-- 支付模块
drop table if exists pay;
create table pay
(
    id            bigint unsigned                                               not null primary key auto_increment,
    out_trade_no  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '支付平台订单号',
    business_type tinyint                                                       not null default 1 comment '业务类型',
    business_id   bigint unsigned                                               not null default 0 comment '业务ID',
    user_id       bigint unsigned                                               not null default 0 comment '用户ID',
    user_openid   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '用户openid',
    pay_type      tinyint                                                       not null default 1 comment '支付方式:1=支付宝,2=微信,3=线下',
    amount        decimal(10, 2)                                                not null default 0.00 comment '支付金额',
    status        tinyint                                                                default 0 not null comment '付款状态:0=未付款,1=付款中,2=已付款,3=退款中,4=已退款',
    remark        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    pay_time      datetime                                                               default null comment '支付时间',
    tenant_id     bigint unsigned                                               not null default 1 comment '租户',
    deleted       tinyint                                                       not null default 0 comment '软删除',
    create_time   datetime                                                               default current_timestamp comment '创建时间',
    update_time   datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, business_type, business_id, user_id, deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='支付记录';



-- 物流模块 begin
-- 快递公司表
CREATE TABLE express_company
(
    id          BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    name        VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '快递公司名称',
    code        VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '快递公司编码',
    status      TINYINT                                    NOT NULL DEFAULT 1 COMMENT '状态:1=启用,2=停用',
    tenant_id   BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME                                            DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX (tenant_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='快递公司';

-- 订单物流表
CREATE TABLE order_express
(
    id                 BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    order_id           BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '订单ID',
    express_company_id BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '快递公司ID',
    express_no         VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '快递单号',
    status             TINYINT                                    NOT NULL DEFAULT 1 COMMENT '状态:1=待发货,2=已发货,3=已签收',
    tenant_id          BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time        DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time        DATETIME                                            DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX (tenant_id, order_id),
    INDEX (tenant_id, express_company_id, express_no)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='订单物流';

-- 物流轨迹表
CREATE TABLE express_track
(
    id               BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    order_express_id BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '订单物流ID',
    content          VARCHAR(255)                               NOT NULL DEFAULT '' COMMENT '轨迹信息',
    status           TINYINT                                    NOT NULL DEFAULT 1 COMMENT '状态:1=运输中,2=已签收,3=异常',
    track_time       DATETIME                                   NOT NULL COMMENT '轨迹时间',
    tenant_id        BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time      DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX (tenant_id, order_express_id, track_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='物流轨迹';
-- 物流模块 end

-- 退款模块 begin
-- 退款申请表
CREATE TABLE refund
(
    id            BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    order_id      BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '订单ID',
    user_id       BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '用户ID',
    refund_no     VARCHAR(64)                                NOT NULL DEFAULT '' COMMENT '退款单号',
    refund_amount DECIMAL(10, 2)                             NOT NULL DEFAULT 0.00 COMMENT '退款金额',
    reason        VARCHAR(255)                               NOT NULL DEFAULT '' COMMENT '退款原因',
    proof_images  TEXT COMMENT '证明图片',
    status        TINYINT                                    NOT NULL DEFAULT 1 COMMENT '状态:1=待处理,2=已同意,3=已拒绝,4=已完成',
    refuse_reason VARCHAR(255)                               NOT NULL DEFAULT '' COMMENT '拒绝原因',
    handle_time   DATETIME                                            DEFAULT NULL COMMENT '处理时间',
    tenant_id     BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time   DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time   DATETIME                                            DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX (tenant_id, order_id),
    INDEX (tenant_id, user_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='退款申请';

-- 退货物流表
CREATE TABLE refund_express
(
    id              BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'ID',
    refund_id       BIGINT UNSIGNED                            NOT NULL DEFAULT 0 COMMENT '退款ID',
    express_company VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '快递公司',
    express_no      VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '快递单号',
    consignee       VARCHAR(50)                                NOT NULL DEFAULT '' COMMENT '收货人',
    telephone       VARCHAR(15)                                NOT NULL DEFAULT '' COMMENT '联系电话',
    address         VARCHAR(255)                               NOT NULL DEFAULT '' COMMENT '收货地址',
    tenant_id       BIGINT UNSIGNED                            NOT NULL DEFAULT 1 COMMENT '租户',
    create_time     DATETIME                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time     DATETIME                                            DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX (tenant_id, refund_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='退货物流';
-- 退款模块 end

-- 分销模块 begin
drop table if exists distributor;
create table distributor
(
    id              bigint unsigned                                               not null primary key auto_increment,
    user_id         bigint unsigned                                               not null default 0 comment '用户ID',
#     name            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    referral_code   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '推荐码',
    parent_user_id  bigint unsigned                                               not null default 0 comment '上级UserID',
    level           tinyint                                                       not null default 0 comment '级别',
    commission_rate decimal(10, 2)                                                not null default 0.00 comment '佣金比例',
    review_status   tinyint                                                       not null default 0 comment '审核状态:0=未审核,1=已审核',
    status          tinyint                                                       not null default 1 comment '状态:1=正常,2=已禁用',
    tenant_id       bigint unsigned                                               not null default 1 comment '租户',
    deleted         tinyint                                                       not null default 0 comment '软删除',
    create_time     datetime                                                               default current_timestamp comment '创建时间',
    update_time     datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id, referral_code, status, deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='分销商';

# 支付完成后才会生成
drop table if exists distributor_order;
create table distributor_order
(
    id            bigint unsigned not null primary key auto_increment,
    user_id       bigint unsigned not null default 0 comment '用户ID',
    order_id      bigint unsigned not null default 0 comment '订单ID',
    commission    decimal(10, 2)  not null default 0.00 comment '佣金',
    status        tinyint         not null default 0 comment '状态:0=未结算,1=已结算',
    complete_time datetime        null comment '结算时间',
    remark        varchar(255)    not null default '' comment '备注',
    tenant_id     bigint unsigned not null default 1 comment '租户',
    deleted       tinyint         not null default 0 comment '软删除',
    create_time   datetime                 default current_timestamp comment '创建时间',
    update_time   datetime                 default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id, order_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='分销商订单';
-- 分销模块 end

-- 用户余额模块 begin
drop table if exists user_money;
create table user_money
(
    id             bigint unsigned                                               not null primary key auto_increment,
    user_id        bigint unsigned                                               not null default 0 comment '用户ID',
    balance        decimal(10, 2)                                                not null default 0.00 comment '余额',
    active_balance decimal(10, 2)                                                not null default 0.00 comment '可用金额',
    frozen_balance decimal(10, 2)                                                not null default 0.00 comment '冻结金额',
    remark         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    status         tinyint                                                       not null default 1 comment '状态:1=正常,1=已禁用',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    deleted        tinyint                                                       not null default 0 comment '软删除',
    create_time    datetime                                                               default current_timestamp comment '创建时间',
    update_time    datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id, status, deleted, balance)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户余额';

drop table if exists user_money_log;
create table user_money_log
(
    id             bigint unsigned                                               not null primary key auto_increment,
    user_id        bigint unsigned                                               not null default 0 comment '用户ID',
    business_type  tinyint                                                       not null default 0 comment '业务类型',
    business_id    bigint unsigned                                               not null default 0 comment '业务ID',
    balance_type   tinyint                                                       not null default 0 comment '余额类型:1=可用余额,2=冻结余额',
    before_balance decimal(10, 2)                                                not null default 0.00 comment '变更前余额',
    change_amount  decimal(10, 2)                                                not null default 0.00 comment '变更金额', #带符号
    after_balance  decimal(10, 2)                                                not null default 0.00 comment '变更后余额',
    remark         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    create_time    datetime                                                               default current_timestamp comment '变动时间',
    index (tenant_id, business_type, business_id, user_id),
    index (tenant_id, user_id, balance_type),
    index (tenant_id, create_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户余额日志';
-- 用户余额模块 end

-- 积分模块 begin
drop table if exists user_credit;
create table user_credit
(
    id          bigint unsigned                                               not null primary key auto_increment,
    user_id     bigint unsigned                                               not null default 0 comment '用户ID',
    balance     decimal(10, 2)                                                not null default 0.00 comment '积分',
    remark      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    status      tinyint                                                       not null default 1 comment '状态:1=正常,1=已禁用',
    tenant_id   bigint unsigned                                               not null default 1 comment '租户',
    deleted     tinyint                                                       not null default 0 comment '软删除',
    create_time datetime                                                               default current_timestamp comment '创建时间',
    update_time datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, user_id, status, deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户积分';

drop table if exists user_credit_log;
create table user_credit_log
(
    id             bigint unsigned                                               not null primary key auto_increment,
    user_id        bigint unsigned                                               not null default 0 comment '用户ID',
    business_type  tinyint                                                       not null default 0 comment '业务类型',
    business_id    bigint unsigned                                               not null default 0 comment '业务ID',
    before_balance decimal(10, 2)                                                not null default 0.00 comment '变更前积分',
    change_amount  decimal(10, 2)                                                not null default 0.00 comment '变更积分', # 带符号
    after_balance  decimal(10, 2)                                                not null default 0.00 comment '变更后积分',
    remark         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    create_time    datetime                                                               default current_timestamp comment '变动时间',
    index (tenant_id, business_type, business_id),
    index (tenant_id, user_id, create_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户积分日志';
-- 积分模块 end

-- 分享模块 begin
drop table if exists share;
create table share
(
    id          bigint unsigned auto_increment primary key not null comment 'ID',
    user_id     bigint unsigned                            not null comment '用户ID',
    share_code  varchar(32)                                not null comment '分享码',             # 可以每次分享不同，也可以每个用户一个
    type        tinyint unsigned                           not null default 1 comment '分享类型', # 分享的渠道，如二维码、朋友圈、微信聊天等
    create_ip   varchar(32)                                not null default '' comment '分享创建IP',
    url         varchar(255)                               not null default '' comment '分享链接',
    tenant_id   bigint unsigned                            not null default 1 comment '租户',
    create_time datetime                                            default current_timestamp comment '创建时间',
    index (tenant_id, user_id),
    index (tenant_id, share_code),
    index (tenant_id, create_ip)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='分享';

drop table if exists share_access_log;
create table share_access_log
(
    id             bigint unsigned auto_increment primary key not null comment 'ID',
    share_id       bigint unsigned                            not null default 0 comment '分享ID',
    access_user_id bigint unsigned                            not null default 0 comment '访问用户ID',
    access_ip      varchar(32)                                not null default '' comment '访问IP',
    tenant_id      bigint unsigned                            not null comment '租户ID',
    create_time    datetime                                            default current_timestamp comment '访问时间',
    index (tenant_id, share_id, create_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='分享访问记录';
-- 分享模块 end

-- 优惠券模块 begin
# 优惠券
drop table if exists coupon;
create table coupon
(
    id                bigint unsigned                                               not null primary key auto_increment,
    name              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '优惠券名称',
    type              tinyint                                                       not null default 2 comment '优惠券类型: 1=指定商品, 2=通用',
    discount_amount   decimal(10, 2)                                                not null default 0.00 comment '满减额度',
    discount_rate     decimal(5, 2)                                                 not null default 0.00 comment '折扣百分比',
    min_order_amount  decimal(10, 2)                                                not null default 0.00 comment '最低订单金额',
    validity_type     tinyint                                                       not null default 1 comment '有效期类型：1=固定日期范围，2=领取后N天',
    valid_days        int                                                           not null default 0 comment '领取后有效天数',
    valid_start_time  datetime                                                               default null comment '有效期开始时间',
    valid_end_time    datetime                                                               default null comment '有效期结束时间',
    total_quantity    int                                                           not null default 0 comment '发放总量',
    received_quantity int                                                           not null default 0 comment '已领取数量',
    used_quantity     int                                                           not null default 0 comment '已使用数量',
    status            tinyint                                                       not null default 1 comment '状态：1=正常，2=已停用',
    tenant_id         bigint unsigned                                               not null default 1 comment '租户',
    deleted           tinyint                                                       not null default 0 comment '软删除',
    create_time       datetime                                                               default current_timestamp comment '创建时间',
    update_time       datetime                                                               default null on update current_timestamp comment '更新时间',
    index (tenant_id, type, deleted, status),
    index (tenant_id, valid_start_time, valid_end_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='优惠券';

# 优惠券商品关联表
drop table if exists coupon_goods;
create table coupon_goods
(
    id          bigint unsigned not null primary key auto_increment,
    coupon_id   bigint unsigned not null comment '优惠券ID',
    goods_id    bigint unsigned not null comment '商品ID',
    tenant_id   bigint unsigned not null default 1 comment '租户',
    create_time datetime                 default current_timestamp comment '创建时间',
    index (tenant_id, coupon_id),
    index (tenant_id, goods_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='优惠券商品关联';

# 用户优惠券
drop table if exists coupon_account;
create table coupon_account
(
    id               bigint unsigned not null primary key auto_increment,
    user_id          bigint unsigned not null comment '用户ID',
    coupon_id        bigint unsigned not null comment '优惠券ID',
    status           tinyint         not null default 1 comment '状态：1=未使用，2=已使用，3=已过期',
    valid_start_time datetime                 default null comment '有效期开始时间',
    valid_end_time   datetime                 default null comment '有效期结束时间',
    use_time         datetime                 default null comment '使用时间',
    tenant_id        bigint unsigned not null default 1 comment '租户',
    create_time      datetime                 default current_timestamp comment '领取时间',
    update_time      datetime                 default null on update current_timestamp comment '更新时间',
    index (tenant_id, user_id, status),
    index (tenant_id, valid_end_time),
    unique index (tenant_id, user_id, coupon_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='优惠券账户';

# 优惠券使用记录
drop table if exists coupon_usage_log;
create table coupon_usage_log
(
    id              bigint unsigned not null primary key auto_increment,
    user_id         bigint unsigned not null comment '用户ID',
    coupon_id       bigint unsigned not null comment '优惠券ID',
    order_id        bigint unsigned not null comment '订单ID',
    discount_amount decimal(10, 2)  not null default 0.00 comment '优惠券抵扣金额',
    order_amount    decimal(10, 2)  not null default 0.00 comment '订单原始金额',
    final_amount    decimal(10, 2)  not null default 0.00 comment '优惠后订单金额',
    tenant_id       bigint unsigned not null default 1 comment '租户',
    create_time     datetime                 default current_timestamp comment '使用时间',
    index (tenant_id, user_id, coupon_id),
    index (tenant_id, order_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='优惠券使用记录';
-- 优惠券模块end


# 用户提现卡
drop table if exists withdraw_card;
create table withdraw_card
(
    id           bigint unsigned auto_increment primary key                    not null comment 'ID',
    user_id      bigint unsigned                                               not null comment '用户ID',
    bank_name    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '开户行',
    account_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '账户名',
    account_no   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '银行卡号',
    tenant_id    bigint unsigned                                               not null default 1 comment '租户',
    create_time  datetime                                                               default current_timestamp comment '创建时间',
    update_time  datetime                                                               default null on update current_timestamp comment '更新时间',
    index (tenant_id, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户提现卡';

# 用户提现记录
drop table if exists withdraw_log;
create table withdraw_log
(
    id            bigint unsigned auto_increment primary key                    not null comment 'ID',
    user_id       bigint unsigned                                               not null comment '用户ID',
    amount        decimal(10, 2)                                                not null default 0.00 comment '提现金额',
    fee           decimal(10, 2)                                                not null default 0.00 comment '手续费',
    bank_name     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '开户行',
    account_name  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '账户名',
    account_no    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '银行卡号',
    status        tinyint                                                       not null default 1 comment '状态:1=待处理,2=成功,3=失败',
    remarks       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    message       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '失败原因',
    tenant_id     bigint unsigned                                               not null default 1 comment '租户',
    complete_time datetime                                                               default null comment '完成时间',
    create_time   datetime                                                               default current_timestamp comment '申请时间',
    update_time   datetime                                                               default null on update current_timestamp comment '更新时间',
    index (tenant_id, user_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='提现记录';


# 2025年08月19日 二次开发

drop table if exists partner_level;
create table partner_level
(
    id                          bigint unsigned auto_increment primary key                    not null comment 'ID',
    name                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    deposit                     decimal(10, 2)                                                not null default 0.00 comment '押金',
    deposit_installment         tinyint                                                       not null default 0 comment '押金可分期:1=是,0=否',
    order_commission_rate       decimal(5, 4)                                                 not null default 0.00 comment '订单提成比例',
    order_commission_repeatable tinyint                                                       not null default 0 comment '订单提成是否可重复:0=否,1=是',
    monthly_commission_rate     decimal(5, 2)                                                 not null default 0.00 comment '月度提成比例',
    annual_commission_rate      decimal(5, 2)                                                 not null default 0.00 comment '年度提成比例',
    tenant_id                   bigint unsigned                                               not null default 1 comment '租户',
    create_time                 datetime                                                               default current_timestamp comment '创建时间',
    update_time                 datetime                                                               default null on update current_timestamp comment '更新时间',
    index (tenant_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人等级';

drop table if exists commission_rate;
create table commission_rate
(
    id               bigint unsigned auto_increment primary key not null comment 'ID',
    partner_level_id bigint unsigned                            not null comment '合伙人等级',
    type             tinyint                                    not null default 1 comment '类型:1=订单,2=月度,3=年度',
    min_amount       decimal(10, 2)                             not null default 0.00 comment '最小金额',
    commission_rate  decimal(5, 4)                              not null default 0.00 comment '佣金比例',
    tenant_id        bigint unsigned                            not null default 1 comment '租户',
    create_time      datetime                                            default current_timestamp comment '创建时间',
    update_time      datetime                                            default null on update current_timestamp comment '更新时间',
    index (tenant_id, partner_level_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人提成';

# 合伙人申请
drop table if exists partner_apply;
create table partner_apply
(
    id                   bigint unsigned auto_increment primary key                     not null comment 'ID',
    user_id              bigint unsigned                                                not null comment '用户ID',
    order_no             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '申请单编号',
    name                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '姓名',
    phone                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '手机号',
    gender               tinyint                                                        not null default 0 comment '性别:0=未知,1=男,2=女',
    email                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '邮箱',
    province             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '省',
    city                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '市',
    district             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '区',
    address              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '详细地址',
    id_card              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '身份证号',
    partner_level_id     bigint unsigned                                                not null comment '合伙人等级',
    resource_description varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '资源描述',
    market_experience    varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '市场经验',
    proxy_area           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '代理区域',
    status               tinyint                                                        not null default 1 comment '状态:1=待审核,2=通过,3=拒绝',
    deposit_amount       decimal(10, 2)                                                 not null default 0.00 comment '已缴押金',
    refuse_reason        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '拒绝原因',
    handle_time          datetime                                                                default null comment '处理时间',
    remark               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '后台备注',
    contract_status      tinyint                                                        not null default 0 comment '合同状态:0=未签署,1=已签署',
    tenant_id            bigint unsigned                                                not null default 1 comment '租户',
    create_time          datetime                                                                default current_timestamp comment '创建时间',
    update_time          datetime                                                                default null on update current_timestamp comment '更新时间',
    index (tenant_id, user_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人申请';

# 合伙人
drop table if exists partner;
create table partner
(
    id               bigint unsigned auto_increment primary key                     not null comment 'ID',
    user_id          bigint unsigned                                                not null comment '用户ID',
    partner_level_id bigint unsigned                                                not null comment '合伙人等级',
    proxy_area       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '代理区域',
    proxy_start_date datetime                                                                default null comment '代理开始时间',
    status           tinyint                                                        not null default 1 comment '状态:1=正常,2=冻结',
    tenant_id        bigint unsigned                                                not null default 1 comment '租户',
    deposit_amount   decimal(10, 2)                                                 not null default 0.00 comment '押金',
    deposit_status   tinyint                                                        not null default 0 comment '押金状态:0=未缴纳,1=部分缴纳,2=完全缴纳,3=已退还',
    remark           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    create_time      datetime                                                                default current_timestamp comment '创建时间',
    update_time      datetime                                                                default null on update current_timestamp comment '更新时间',
    index (tenant_id, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人';

drop table if exists partner_deposit_log;
create table partner_deposit_log
(
    id            bigint unsigned auto_increment primary key                    not null comment 'ID',
    partner_id    bigint unsigned                                               not null comment '合伙人ID',
    type          tinyint                                                       not null default 0 comment '类型:1=主动缴纳,2=提成抵扣,3=退还',
    before_amount decimal(10, 2)                                                not null default 0.00 comment '前金额',
    change_amount decimal(10, 2)                                                not null default 0.00 comment '变动金额',
    after_amount  decimal(10, 2)                                                not null default 0.00 comment '后金额',
    remark        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id     bigint unsigned                                               not null default 1 comment '租户',
    create_time   datetime                                                               default current_timestamp comment '创建时间',
    index (tenant_id, partner_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人押金记录';


# 用户档案
drop table if exists user_profile;
create table user_profile
(
    id              bigint unsigned auto_increment primary key                    not null comment 'ID',
    user_id         bigint unsigned                                               not null comment '用户ID',
    partner_user_id bigint unsigned                                               not null default 0 comment '所属合伙人',
    customer_level  tinyint                                                       not null default 1 comment '客户级别:1=普通客户,2=大客户',
    name            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '姓名',
    avatar          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '头像',
    gender          tinyint                                                       not null default 0 comment '性别:0=未知,1=男,2=女',
    phone           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '手机号',
    email           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '邮箱',
    birthday        date                                                                   default null comment '生日',
    province        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '省',
    city            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '市',
    district        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '区',
    address         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '详细地址',
    id_card         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '身份证号',
    tenant_id       bigint unsigned                                               not null default 1 comment '租户',
    create_time     datetime                                                               default current_timestamp comment '创建时间',
    update_time     datetime                                                               default null on update current_timestamp comment '更新时间',
    index (tenant_id, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户档案';

drop table if exists partner_order_commission;
create table partner_order_commission
(
    id          bigint unsigned auto_increment primary key not null comment 'ID',
    partner_id  bigint unsigned                            not null comment '合伙人ID',
    order_id    bigint unsigned                            not null comment '订单ID',
    amount      decimal(10, 2)                             not null default 0.00 comment '提成金额',
    status      tinyint                                    not null default 1 comment '状态:1=待入账,2=已入账,3=已拒绝',
    tenant_id   bigint unsigned                            not null default 1 comment '租户',
    create_time datetime                                            default current_timestamp comment '创建时间',
    update_time datetime                                            default null on update current_timestamp comment '更新时间',
    index (tenant_id, partner_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人订单提成';

drop table if exists partner_monthly_commission;
create table partner_monthly_commission
(
    id           bigint unsigned auto_increment primary key                  not null comment 'ID',
    partner_id   bigint unsigned                                             not null comment '合伙人ID',
    `year_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '年月:YYYY-MM',
    amount       decimal(10, 2)                                              not null default 0.00 comment '提成金额',
    status       tinyint                                                     not null default 1 comment '状态:1=待入账,2=已入账,3=已拒绝',
    tenant_id    bigint unsigned                                             not null default 1 comment '租户',
    create_time  datetime                                                             default current_timestamp comment '创建时间',
    update_time  datetime                                                             default null on update current_timestamp comment '更新时间',
    index (tenant_id, partner_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人月度提成';

drop table if exists partner_annual_commission;
create table partner_annual_commission
(
    id          bigint unsigned auto_increment primary key not null comment 'ID',
    partner_id  bigint unsigned                            not null comment '合伙人ID',
    `year`      YEAR                                       not null comment '年份',
    amount      decimal(10, 2)                             not null default 0.00 comment '提成金额',
    status      tinyint                                    not null default 1 comment '状态:1=待入账,2=已入账,3=已拒绝',
    tenant_id   bigint unsigned                            not null default 1 comment '租户',
    create_time datetime                                            default current_timestamp comment '创建时间',
    update_time datetime                                            default null on update current_timestamp comment '更新时间',
    index (tenant_id, partner_id, status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='合伙人年度提成';

drop table if exists tiered_pricing_rule;
CREATE TABLE tiered_pricing_rule
(
    id           bigint unsigned auto_increment primary key not null comment 'ID',
    sku_id       bigint unsigned                            not null comment '商品ID',
    min_quantity int                                        not null default 0 comment '最小数量',
    max_quantity int                                        not null default 0 comment '最大数量:-1代表无上限',
    price        decimal(10, 2)                             not null default 0.00 comment '价格',
    tenant_id    bigint unsigned                            not null default 1 comment '租户',
    create_time  datetime                                            default current_timestamp comment '创建时间',
    update_time  datetime                                            default null on update current_timestamp comment '更新时间',
    index (tenant_id, sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='阶梯定价规则';

drop table if exists customer_pricing_rule;
CREATE TABLE customer_pricing_rule
(
    id          bigint unsigned auto_increment primary key not null comment 'ID',
    sku_id      bigint unsigned                            not null comment '商品ID',
    user_id     bigint unsigned                            not null comment '用户ID',
    price       decimal(10, 2)                             not null default 0.00 comment '价格',
    tenant_id   bigint unsigned                            not null default 1 comment '租户',
    create_time datetime                                            default current_timestamp comment '创建时间',
    update_time datetime                                            default null on update current_timestamp comment '更新时间',
    index (tenant_id, sku_id),
    index (tenant_id, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='客户定价规则';