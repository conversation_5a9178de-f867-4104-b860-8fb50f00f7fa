create table tenant
(
    id          bigint unsigned auto_increment primary key                   not null comment '编号',
    name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',

    status      tinyint unsigned                                             not null default 0 comment '状态', # 0:未启用 1:启用 2:过期
    deleted     tinyint                                                      not null default 0 comment '软删除',
    create_time datetime                                                              default current_timestamp comment '创建时间',
    update_time datetime                                                              default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='租户';

create table user
(
    id          bigint unsigned auto_increment primary key                   not null comment '编号',
    name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',

    mobile      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '手机号',
    password    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '密码',
    type        tinyint unsigned                                             not null default 0 comment '类型',
    status      tinyint unsigned                                             not null default 0 comment '状态', #
    tenant_id   int unsigned                                                 not null default 1 comment '租户',
    deleted     tinyint                                                      not null default 0 comment '软删除',
    create_time datetime                                                              default current_timestamp comment '创建时间',
    update_time datetime                                                              default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户';