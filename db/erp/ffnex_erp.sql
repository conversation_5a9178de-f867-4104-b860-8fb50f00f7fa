# 设计原则：
# 负库存，数据库无限制，在业务中根据情况设置
# 成本采用移动加权平均法，不允许负库存 数据库无限制
# 商品无批号序列号，也无出库顺序，库存加入批号/序列号，暂不支持（先进先出法需要这些字段的支持）
# 一般不允许跨仓库开单 数据库结构限制；当然业务单也可以不关联仓库，在出入库时选择仓库
# 退单出入库时的成本，按照原业务发生时的成本核算，实际成本法
# 销售下单时是否检查库存、下单后是否锁定库存
# 仓库成本是否独立核算？
# 子表中是否需要维护创建、更新时间的字段

create database youxuan_erp default charset utf8mb4 collate utf8mb4_general_ci;

use youxuan_erp;

create table if not exists warehouse
(
    id                 bigint unsigned auto_increment primary key                    not null comment 'ID',
    warehouse_sn       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '仓库编码',
    name               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '名称',
    remark             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    inventory_quantity decimal(11, 2)                                                not null default 0 comment '库存总量',
    inventory_amount   decimal(11, 2)                                                not null default 0 comment '库存总额',
    status             tinyint                                                       not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    weigh              int                                                           not null default 0 comment '排序',
    tenant_id          bigint unsigned                                               not null default 1 comment '租户',
    deleted            tinyint                                                       not null default 0 comment '软删除',
    create_time        datetime                                                               default current_timestamp comment '创建时间',
    update_time        datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (warehouse_sn)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='仓库';

create table if not exists category
(
    id          bigint unsigned auto_increment primary key                   not null comment 'ID',
    category_sn varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '分类编码',
    name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    pid         bigint unsigned                                              not null default 0 comment '上层ID',
    weigh       int                                                          not null default 0 comment '排序',
    status      tinyint                                                      not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    tenant_id   bigint unsigned                                              not null default 1 comment '租户',
    deleted     tinyint                                                      not null default 0 comment '软删除',
    create_time datetime                                                              default current_timestamp comment '创建时间',
    update_time datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (category_sn, name)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品分类';

create table if not exists brand
(
    id          bigint unsigned auto_increment primary key                   not null comment 'ID',
    brand_sn    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '品牌编码',
    name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    weigh       int                                                          not null default 0 comment '排序',
    status      tinyint                                                      not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    tenant_id   bigint unsigned                                              not null default 1 comment '租户',
    deleted     tinyint                                                      not null default 0 comment '软删除',
    create_time datetime                                                              default current_timestamp comment '创建时间',
    update_time datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (brand_sn, name)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='品牌';

create table if not exists goods
(
    id           bigint unsigned auto_increment primary key                   not null comment '编码',
    goods_sn     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品编码',
    name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    category_id  bigint unsigned                                              not null default 0 comment '分类',
    brand_id     bigint unsigned                                              not null default 0 comment '品牌',
    weigh        int                                                          not null default 0 comment '排序',
    spu_group_id bigint unsigned                                              not null default 0 comment '品类',               # 品类的意义在于决定商品与sku无关的属性，如产地、包装等
    status       tinyint                                                      not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    tenant_id    bigint unsigned                                              not null default 1 comment '租户',
    deleted      tinyint                                                      not null default 0 comment '软删除',
    create_time  datetime                                                              default current_timestamp comment '创建时间',
    update_time  datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (goods_sn),
    index (name),
    index (category_id),
    index (brand_id),
    index (spu_group_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品';

create table if not exists goods_sku
(
    id             bigint unsigned auto_increment primary key                   not null comment 'ID',
    goods_sku_sn   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment 'SKU编码',
    goods_id       bigint unsigned                                              not null default 0 comment '商品ID',
    goods_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品名称',
    name           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    mnemonic       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '助记码',
    specification  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '规格',
    unit           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '单位',
    purchase_price decimal(11, 2)                                               not null default 0 comment '采购价',
    sale_price     decimal(11, 2)                                               not null default 0 comment '销售价',
    weigh          int                                                          not null default 0 comment '排序',
    tenant_id      bigint unsigned                                              not null default 1 comment '租户',
    deleted        tinyint                                                      not null default 0 comment '软删除',
    create_time    datetime                                                              default current_timestamp comment '创建时间',
    update_time    datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (deleted, tenant_id),
    index (goods_sku_sn),
    index (name),
    index (mnemonic)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品SKU';

# 品类决定商品的属性集合
create table if not exists spu_group
(
    id           bigint unsigned auto_increment primary key                   not null comment '编码',
    spu_group_sn varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '品类编码',
    name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    weigh        int                                                          not null default 0 comment '排序',
    status       tinyint                                                      not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    tenant_id    bigint unsigned                                              not null default 1 comment '租户',
    deleted      tinyint                                                      not null default 0 comment '软删除',
    create_time  datetime                                                              default current_timestamp comment '创建时间',
    update_time  datetime                                                              default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品品类';

create table if not exists `attribute`
(
    id           bigint unsigned auto_increment primary key                   not null comment '编码',
    attribute_sn varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '属性编码',
    name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    spu_group_id bigint unsigned                                              not null default 0 comment '品类',
    weigh        int                                                          not null default 0 comment '排序',
    tenant_id    bigint unsigned                                              not null default 1 comment '租户',
    deleted      tinyint                                                      not null default 0 comment '软删除',
    create_time  datetime                                                              default current_timestamp comment '创建时间',
    update_time  datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (deleted, tenant_id),
    index (spu_group_id),
    index (name)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品属性';

# 功能是便于用户输入，商品实际存储的是name值
create table if not exists attribute_option
(
    id           bigint unsigned auto_increment primary key                   not null comment '编码',
    name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    attribute_id bigint unsigned                                              not null default 0 comment '属性',
    weigh        int                                                          not null default 0 comment '排序',
    tenant_id    bigint unsigned                                              not null default 1 comment '租户',
    deleted      tinyint                                                      not null default 0 comment '软删除',
    create_time  datetime                                                              default current_timestamp comment '创建时间',
    update_time  datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (deleted, tenant_id),
    index (attribute_id),
    index (name)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='属性选项';

# 关联表，无需创建实体以及相关组件
create table if not exists goods_attribute_value
(
    id              bigint unsigned auto_increment primary key                   not null comment '编码',
    goods_id        bigint unsigned                                              not null default 0 comment '商品',
    attribute_id    bigint unsigned                                              not null default 0 comment '属性',
    attribute_value varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '属性值', #来源属性选项和用户输入
    index (goods_id, attribute_id, attribute_value)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品属性关联';

create table if not exists customer
(
    id                bigint unsigned auto_increment primary key                    not null comment '编码',
    customer_sn       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '客户编码',
    name              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '名称',
    contact_name      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '联系人',
    sale_amount       decimal(11, 2)                                                not null default 0.00 comment '销售总额',
    receivable_amount decimal(11, 2)                                                not null default 0.00 comment '应收账款',
    account_name      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '银行户名',
    account_no        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '银行账号',
    account_bank      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '开户银行',
    mobile            varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '手机号',
    province          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '省份',
    city              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '城市',
    district          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '地区',
    address           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '详细地址',
    remark            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    weigh             int                                                           not null default 0 comment '排序',
    create_user_id    bigint unsigned                                               not null default 0 comment '创建人',
    create_user_name  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '创建人',
    tenant_id         bigint unsigned                                               not null default 1 comment '租户',
    deleted           tinyint                                                       not null default 0 comment '软删除',
    create_time       datetime                                                               default current_timestamp comment '创建时间',
    update_time       datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (deleted, tenant_id),
    index (customer_sn),
    index (name),
    index (contact_name),
    index (mobile)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='客户';

# 销售订单必须指定仓库
create table if not exists sale_order
(
    id               bigint unsigned auto_increment primary key                    not null comment 'ID',
    order_sn         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '销售单号',# XS+短日期+4位流水号
    customer_id      bigint unsigned                                               not null default 0 comment '客户',
    warehouse_id     bigint unsigned                                               not null default 0 comment '仓库',
    warehouse_name   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '仓库名称',
    customer_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '客户名称', # 冗余
    total_amount     decimal(11, 2)                                                not null default 0.00 comment '总金额', # 不包含优惠金额、抹零金额等 sum(item.sale_price * quantity)
    discount_amount  decimal(11, 2)                                                not null default 0.00 comment '优惠金额', # 开单时直接优惠的金额 item.calc_price = (total_amount - discount_amount - change_amount) / total_amount * item.sale_price
    change_amount    decimal(11, 2)                                                not null default 0.00 comment '抹零金额', # 付款后的减免的部分金额 item.calc_price = (total_amount - discount_amount - change_amount) / total_amount * item.sale_price
    pay_amount       decimal(11, 2)                                                not null default 0.00 comment '已付款额', #与收款单同步
    outbound_status tinyint unsigned                                              not null default 0 comment '出库状态:0=未出库,1=已出库,2=部分出库',# 0未出库，1已出库 2部分出库
    review_status    tinyint                                                       not null default 0 comment '审核状态:0=未审核,1=已审核', # 审核后的订单才可以进行后续操作（销售业绩统计、出库等，审核前可终止、修改等）、可以反审核
    status           tinyint                                                       not null default 1 comment '订单状态:1=正常,2=终止', # 终止订单不计入统计，关联的出库、收款等关数据也同时终止
    remark           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    sale_user_id     bigint unsigned                                               not null default 0 comment '销售员ID',
    sale_user_name   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '销售员',
    create_user_id   bigint unsigned                                               not null default 0 comment '开单人ID',
    create_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '开单人',
    review_user_id   bigint unsigned                                               not null default 0 comment '审核人ID',
    review_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '审核人',
    sale_date        date                                                          not null default '1111-11-11' comment '销售日期',
    review_time      datetime                                                               default null comment '审核时间',
    tenant_id        bigint unsigned                                               not null default 1 comment '租户', # 删除与终止的区别在于前台可见性
    deleted          tinyint                                                       not null default 0 comment '软删除',
    create_time      datetime                                                               default current_timestamp comment '创建时间',
    update_time      datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (customer_id),
    index (outbound_status),
    index (review_status),
    index (sale_user_id),
    index (sale_date)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售订单';

drop table if exists sale_order_item;
create table if not exists sale_order_item
(
    id             bigint unsigned auto_increment primary key                    not null comment 'ID',
    sale_order_id  bigint unsigned                                               not null default 0 comment '销货单',
    goods_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品名称',
    goods_sku_id   bigint unsigned                                               not null default 0 comment '商品SKU',
    goods_sku_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品SKU名称',
    sale_price     decimal(11, 2)                                                not null default 0.00 comment '销售单价', # 实际单价
    calc_price     decimal(11, 2)                                                not null default 0.00 comment '核算单价', # 扣除优惠、抹零后的实际单价，意义在于销售统计，出库业绩统计（非成本）等
    quantity       decimal(11, 2)                                                not null default 0 comment '数量',
    unit           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '单位',
    amount         decimal(11, 2)                                                not null default 0.00 comment '销售金额', # sale_price * quantity
    remark         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    deleted        tinyint                                                       not null default 0 comment '软删除',      #订单删除同时删除订单项
    create_time    datetime                                                               default current_timestamp comment '创建时间',
    update_time    datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (sale_order_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售明细';

# 出库单
# 出库单与仓库关联，跨库出库使用库存调拨处理；出库单与业务单关联，冗余业务单号
# 出库操作不可撤销，只可以退货
create table if not exists inventory_outbound
(
    id                 bigint unsigned auto_increment primary key                    not null comment '编码',
    outbound_sn        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '出库单号',                                #CK + 年月日+流水号
    business_type      tinyint unsigned                                              not null default 0 comment '出库类型:1=销售,2=进货退货,3=调出,4=盘亏', # 1销售 2进货退货 3调出 4盘亏；
    business_id        bigint unsigned                                               not null default 0 comment '业务ID',# 与business_type一起关联对应业务单，如多对一关联sale_order，销售订单分批次出库
    business_sn        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '业务编号',#冗余
    warehouse_id       bigint unsigned                                               not null default 0 comment '仓库',
    warehouse_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '仓库名称',
    total_cost         decimal(11, 2)                                                not null default 0.00 comment '总成本',                                # item.unit_cost * item.quantity
    remark             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    review_status      tinyint                                                       not null default 0 comment '审核状态:0=未审核,1=审核通过',             # 审核后方可以执行出库操作，审核前可作废或删除
    status             tinyint                                                       not null default 1 comment '状态:1=正常,2=作废',                       # 作废后不允许再执行出库操作，不计入统计
    create_user_id     bigint unsigned                                               not null default 0 comment '创建人ID',
    create_user_name   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '创建人',
    review_user_id     bigint unsigned                                               not null default 0 comment '审核人ID',
    review_user_name   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '审核人',
    review_time        datetime                                                               default null comment '审核时间',
    outbound_status    tinyint                                                       not null default 0 comment '出库状态:0=未出库,1=已出库',               # 出库后退货应该走退货入库流程，出库不可撤销
    outbound_time      datetime                                                               default null comment '出库时间',
    outbound_user      bigint unsigned                                               not null default 0 comment '出库人',
    outbound_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '出库人',
    tenant_id          bigint unsigned                                               not null default 1 comment '租户',
    deleted            tinyint                                                       not null default 0 comment '软删除',                                   # 删除与作废的区别是前台可见性
    create_time        datetime                                                               default current_timestamp comment '创建时间',
    update_time        datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (warehouse_id),
    index (business_type, business_id),
    index (review_status),
    index (outbound_status),
    index (outbound_sn)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='出库单';

# 出库单item
# 不与业务单的item关联，通过主表与业务主表关联
drop table if exists inventory_outbound_item;
create table if not exists inventory_outbound_item
(
    id                    bigint unsigned auto_increment primary key                    not null comment 'ID',
    inventory_outbound_id bigint unsigned                                               not null default 0 comment '出库单',
    goods_name            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品名称',
    goods_sku_id          bigint unsigned                                               not null default 0 comment '商品SKU',
    goods_sku_name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品SKU名称',
    quantity              decimal(11, 2)                                                not null default 0 comment '出库数量',
    unit                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '单位',
    unit_cost             decimal(11, 2)                                                not null default 0.00 comment '单位成本',
    cost_amount           decimal(11, 2)                                                not null default 0.00 comment '成本总额', # item.unit_cost * item.quantity
    calc_sale_price       decimal(11, 2)                                                not null default 0.00 comment '销售单价', # 冗余，来源与sale_order.item中的calc_price ,便于精细化计算逐笔出库项的销售利润
    remark                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id             bigint unsigned                                               not null default 1 comment '租户',
    deleted               tinyint                                                       not null default 0 comment '软删除',
    create_time           datetime                                                               default current_timestamp comment '创建时间',
    update_time           datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (inventory_outbound_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='出库明细';

# 进货订单
create table if not exists purchase_order
(
    id               bigint unsigned auto_increment primary key                    not null comment 'ID',
    order_sn         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '进货单号',#JH + 日期 + 0001
    warehouse_id     bigint unsigned                                               not null default 0 comment '仓库',
    warehouse_name   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '仓库名称',
    supplier_id      bigint unsigned                                               not null default 0 comment '供应商ID',
    supplier_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '供应商',
    remark           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    review_status    tinyint                                                       not null default 0 comment '审核状态:0=未审核,1=审核通过',
    inbound_status tinyint                                                       not null default 0 comment '入库状态:0=未入库,1=已入库,2=部分入库',
    total_amount     decimal(11, 2)                                                not null default 0.00 comment '总金额', # 进货总金额
#     total_quantity   decimal(11, 2)                                                not null default 0.00 comment '总数量', # 进货总数量 统计意义不大，另外是否需要显示 已入库的数量、金额
    purchase_date    date                                                          not null default '1111-11-11' comment '进货日期',
    create_user_id   bigint unsigned                                               not null default 0 comment '创建人',
    create_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '创建人名称',
    review_user_id   bigint unsigned                                               not null default 0 comment '审核人',
    review_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '审核人名称',
    review_time      datetime                                                      null comment '审核时间',
    status           tinyint                                                       not null default 0 comment '状态:1=正常,2=作废',
    tenant_id        bigint unsigned                                               not null default 1 comment '租户',
    deleted          tinyint                                                       not null default 0 comment '软删除',
    create_time      datetime                                                               default current_timestamp comment '创建时间',
    update_time      datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (order_sn),
    index (warehouse_id),
    index (supplier_id),
    index (purchase_date),
    index (review_status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='进货订单';

# 进货订单明细
create table if not exists purchase_order_item
(
    id                bigint unsigned auto_increment primary key                    not null comment '编码',
    purchase_order_id bigint unsigned                                               not null default 0 comment '进货订单',
    goods_name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品名称',
    goods_sku_id      bigint unsigned                                               not null default 0 comment '商品SKU',
    goods_sku_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品SKU名称',
    quantity          decimal(11, 2)                                                not null default 0.00 comment '数量',
    unit             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '单位',
    unit_cost         decimal(11, 2)                                                not null default 0.00 comment '单位成本',
    amount            decimal(11, 2)                                                not null default 0.00 comment '进货金额', # quantity * unit_cost
    remark            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id         bigint unsigned                                               not null default 1 comment '租户',
    deleted           tinyint                                                       not null default 0 comment '软删除',
    create_time       datetime                                                               default current_timestamp comment '创建时间',
    update_time       datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (purchase_order_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='进货明细';

# 供应商
create table if not exists supplier
(
    id              bigint unsigned auto_increment primary key                    not null comment '编码',
    name            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '名称',
    contact_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '联系人',
    mobile          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '手机号',
    payable_amount  decimal(11, 2)                                                not null default 0.00 comment '应付账款',        # purchase_amount  - payment_amount
    purchase_amount decimal(11, 2)                                                not null default 0.00 comment '采购总额',
    payment_amount  decimal(11, 2)                                                not null default 0.00 comment '付款总额',
    account_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '银行户名',
    account_no      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '银行账号',
    account_bank    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '开户银行',
    province        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '省份',
    city            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '城市',
    district        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '区县',
    address         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '地址',
    remark          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    status          tinyint                                                       not null default 1 comment '状态:1=启用,2=停用', # 1:启用 2:停用
    weigh           int                                                           not null default 0 comment '排序',
    tenant_id       bigint unsigned                                               not null default 1 comment '租户',
    deleted         tinyint                                                       not null default 0 comment '软删除',
    create_time     datetime                                                               default current_timestamp comment '创建时间',
    update_time     datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (name),
    index (contact_name),
    index (mobile)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='供应商';

# 入库单
# 业务单与入库单是一对多关系，1个业务单可以有多个入库单，如采购订单可以分多次入库
# 入库单只有审核后才会触发库存变动
create table if not exists inventory_inbound
(
    id                bigint unsigned auto_increment primary key                    not null comment '编码',
    inbound_sn        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '入库单号',
    business_type     tinyint unsigned                                              not null default 0 comment '入库类型:1=进货,2=销售退货,3=调入,4=盘盈', # 1进货 2销售退货 3调入 4盘盈；
    business_id       bigint unsigned                                               not null default 0 comment '业务ID',# 与business_type一起关联对应业务单，如多对一关联purchase_order，进货订单分批次入库
    business_sn       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '业务编号',#冗余
    warehouse_id      bigint unsigned                                               not null default 0 comment '仓库',
    warehouse_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '仓库名称',
    total_cost        decimal(11, 2)                                                not null default 0.00 comment '入库总金额',
    remark            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    review_status     tinyint unsigned                                              not null default 0 comment '审核状态:0=待审核,1=审核通过',
    inbound_status    tinyint unsigned                                              not null default 0 comment '入库状态:0=待入库,1=已入库',
    status            tinyint                                                       not null default 1 comment '状态:1=正常,2=作废',                       # 1正常，2作废
    create_user_id    bigint unsigned                                               not null default 0 comment '创建人',
    create_user_name  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '创建人',
    review_user_id    bigint unsigned                                               not null default 0 comment '审核人',
    review_user_name  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '审核人',
    review_time       datetime                                                               default null comment '审核时间',
    inbound_user_id   bigint unsigned                                               not null default 0 comment '入库人',
    inbound_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '入库人',
    inbound_time      datetime                                                               default null comment '入库时间',
    tenant_id         bigint unsigned                                               not null default 1 comment '租户',
    deleted           tinyint                                                       not null default 0 comment '软删除',
    create_time       datetime                                                               default current_timestamp comment '创建时间',
    update_time       datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (business_type, business_id),
    index (inbound_sn),
    index (warehouse_id),
    index (inbound_status),
    index (review_status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='入库单';

drop table if exists inventory_inbound_item;
create table if not exists inventory_inbound_item
(
    id                   bigint unsigned auto_increment primary key                    not null comment '编码',
    inventory_inbound_id bigint unsigned                                               not null default 0 comment '入库单',
    goods_name           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品名称',
    goods_sku_id         bigint unsigned                                               not null default 0 comment '商品SKU',
    goods_sku_name       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品SKU名称',
    quantity             decimal(11, 2)                                                not null default 0 comment '入库数量',
    unit_cost            decimal(11, 2)                                                not null default 0.00 comment '入库成本',
    unit                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品单位',
    amount               decimal(11, 2)                                                not null default 0.00 comment '入库金额',
    remark               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id            bigint unsigned                                               not null default 1 comment '租户',
    deleted              tinyint                                                       not null default 0 comment '软删除',
    create_time          datetime                                                               default current_timestamp comment '创建时间',
    update_time          datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (inventory_inbound_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='入库明细';

# 即时库存与当前商品sku设置相关，所以必定要关联查询sku以及goods表
# 当前库存与库存报表不同，库存报表需要group仓库后的统计
# 不同仓库分别统计
create table if not exists inventory
(
    id                 bigint unsigned auto_increment primary key not null comment '编码',
    goods_sku_id       bigint unsigned                            not null default 0 comment '商品SKU',
    warehouse_id       bigint unsigned                            not null default 0 comment '仓库',
    quantity           decimal(11, 2)                             not null default 0 comment '即时库存',
    occupied_quantity  decimal(11, 2)                             not null default 0 comment '锁定库存',
    available_quantity decimal(11, 2)                             not null default 0 comment '可用库存',                  # quantity - locked_quantity
    unit_cost          decimal(11, 2)                             not null default 0.00 comment '单位成本',
    amount             decimal(11, 2)                             not null default 0.00 comment '库存总额',
    locked_status      tinyint                                    not null default 0 comment '锁定状态:0=未锁定,1=已锁定',# 锁定期间，如盘存期间，库存不允许变动
    tenant_id          bigint unsigned                            not null default 1 comment '租户',
    deleted            tinyint                                    not null default 0 comment '软删除',
    create_time        datetime                                            default current_timestamp comment '创建时间',
    update_time        datetime                                            default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (goods_sku_id),
    index (warehouse_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='当前库存';

# 库存变动记录，便于展示流水，里面的数据输入冗余内容，仅做展示，减少查询时的开销
create table if not exists inventory_log
(
    id             bigint unsigned auto_increment primary key                    not null comment '编码',
    business_type  tinyint                                                       not null default 0 comment '业务类型:1=入库,-1=出库',# 目的是便于追溯变动来源：业务关联入库单与出库单
    business_id    bigint unsigned                                               not null default 0 comment '业务ID',
    business_sn    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '业务单号',              # 出入库单的单号
    warehouse_id   bigint unsigned                                               not null default 0 comment '仓库',
    warehouse_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '仓库名称',
    goods_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品名称',              #冗余
    goods_sku_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品SKU名称',           # 冗余
    goods_sku_id   bigint unsigned                                               not null default 0 comment '商品SKU编号',            # 冗余
    goods_unit     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品单位',              # 冗余,防止修改sku单位后影响历史数据
    pre_quantity   decimal(11, 2)                                                not null default 0 comment '变动前数量',
    quantity       decimal(11, 2)                                                not null default 0 comment '变动数量',
    post_quantity  decimal(11, 2)                                                not null default 0 comment '变动后数量',
    pre_unit_cost  decimal(11, 2)                                                not null default 0.00 comment '变动前成本',
    unit_cost      decimal(11, 2)                                                not null default 0.00 comment '变动成本',            # 库存变动时携带的单位成本，比如进货入库，即为入库的单位成本，采购退货时，为采购入库的成本
    post_unit_cost decimal(11, 2)                                                not null default 0.00 comment '变动后成本',
    pre_amount     decimal(11, 2)                                                not null default 0.00 comment '变动前金额',
    amount         decimal(11, 2)                                                not null default 0.00 comment '变动金额',
    post_amount    decimal(11, 2)                                                not null default 0.00 comment '变动后金额',
    remark         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    deleted        tinyint                                                       not null default 0 comment '软删除',
    create_time    datetime                                                               default current_timestamp comment '发生时间',
    update_time    datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (business_type, business_id),
    index (goods_sku_id),
    index (warehouse_id),
    index (create_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='库存流水';



# todo:暂时不开发--------------------------------------------------------------------
# 销售退货单
create table if not exists sale_return
(
    id               bigint unsigned auto_increment primary key                    not null comment 'ID',
    return_sn        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '退货单号',
    sale_order_id    bigint unsigned                                               not null default 0 comment '销售单ID',
    sale_order_sn    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '销售单号',
    customer_id      bigint unsigned                                               not null default 0 comment '客户ID',
    customer_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '客户名称',
    total_amount     decimal(11, 2)                                                not null default 0.00 comment '退货总金额', # 支付给客户的退款金额
    total_cost       decimal(11, 2)                                                not null default 0.00 comment '成本总金额', # 退货入库的成本总金额
    remark           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    status           tinyint unsigned                                              not null default 0 comment '状态:1=正常,2=作废',
    create_user_id   bigint unsigned                                               not null default 0 comment '创建人ID',
    create_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '创建人名称',
    return_date      datetime                                                               default null comment '退货日期',
    review_status    tinyint unsigned                                              not null default 0 comment '审核状态:0=待审核,1=已审核',
    review_user_id   bigint unsigned                                               not null default 0 comment '审核人ID',
    review_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '审核人名称',
    review_time      datetime                                                      null comment '审核时间',
    inventory_status tinyint unsigned                                              not null default 0 comment '入存状态:0=未入库,1=已入库,2=部分入库',
    tenant_id        bigint unsigned                                               not null default 1 comment '租户',
    deleted          tinyint                                                       not null default 0 comment '软删除',
    create_time      datetime                                                               default current_timestamp comment '创建时间',
    update_time      datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (sale_order_id),
    index (customer_id),
    index (return_sn),
    index (review_status),
    index (inventory_status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售退货单';

# 销售退货单明细
create table if not exists sale_return_item
(
    id             bigint unsigned auto_increment primary key                    not null comment 'ID',
    sale_return_id bigint unsigned                                               not null default 0 comment '销售退货单ID',
    goods_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品名称',
    goods_sku_id   bigint unsigned                                               not null default 0 comment '商品SKU',
    goods_sku_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品SKU名称',
    quantity       decimal(11, 2)                                                not null default 0 comment '数量',
    unit_price     decimal(11, 2)                                                not null default 0.00 comment '退货单价', # 开单可改，来源orderItem.calc_price
    unit_cost      decimal(11, 2)                                                not null default 0.00 comment '单位成本',
    amount         decimal(11, 2)                                                not null default 0.00 comment '退货金额',
    cost_amount    decimal(11, 2)                                                not null default 0.00 comment '成本金额',
    remark         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id      bigint unsigned                                               not null default 1 comment '租户',
    deleted        tinyint                                                       not null default 0 comment '软删除',
    create_time    datetime                                                               default current_timestamp comment '创建时间',
    update_time    datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (sale_return_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售退单明细';

# 进货退单
create table if not exists purchase_return
(
    id                bigint unsigned auto_increment primary key                    not null comment 'ID',
    return_sn         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '退货单号',
    purchase_order_id bigint unsigned                                               not null default 0 comment '采购订单ID',
    purchase_order_sn varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '采购订单号',
    supplier_id       bigint unsigned                                               not null default 0 comment '供应商ID',
    supplier_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '供应商名称',
    total_amount      decimal(11, 2)                                                not null default 0.00 comment '退货总金额', # 收取供应商的退款金额
    total_cost        decimal(11, 2)                                                not null default 0.00 comment '成本总金额', # 退货出库的成本总额
    remark            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    status            tinyint unsigned                                              not null default 0 comment '状态:1=正常,2=作废',
    create_user_id    bigint unsigned                                               not null default 0 comment '创建人ID',
    create_user_name  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '创建人名称',
    return_date       datetime                                                               default null comment '退货日期',
    review_status     tinyint unsigned                                              not null default 0 comment '审核状态:0=待审核,1=已审核',
    review_user_id    bigint unsigned                                               not null default 0 comment '审核人ID',
    review_user_name  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '审核人名称',
    review_time       datetime                                                      null comment '审核时间',
    inventory_status  tinyint unsigned                                              not null default 0 comment '出存状态:0=未出库,1=已出库,2=部分出库',
    tenant_id         bigint unsigned                                               not null default 1 comment '租户',
    deleted           tinyint                                                       not null default 0 comment '软删除',
    create_time       datetime                                                               default current_timestamp comment '创建时间',
    update_time       datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (purchase_order_id),
    index (supplier_id),
    index (return_sn),
    index (review_status),
    index (inventory_status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='进货退货单';

create table if not exists purchase_return_item
(
    id                 bigint unsigned auto_increment primary key                   not null comment 'ID',
    purchase_return_id bigint unsigned                                              not null default 0 comment '退货单ID',
    goods_sku_id       bigint unsigned                                              not null default 0 comment '商品SKU',
    goods_name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品名称',
    goods_sku_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品SKU名称',
    quantity           int unsigned                                                 not null default 0 comment '退货数量',
    unit_price         decimal(11, 2)                                               not null default 0.00 comment '退货单价',
    amount             decimal(11, 2)                                               not null default 0.00 comment '退货金额',
    unit_cost          decimal(11, 2)                                               not null default 0.00 comment '退货成本单价',
    cost_amount        decimal(11, 2)                                               not null default 0.00 comment '成本金额',
    tenant_id          bigint unsigned                                              not null default 1 comment '租户',
    deleted            tinyint                                                      not null default 0 comment '软删除',
    create_time        datetime                                                              default current_timestamp comment '创建时间',
    update_time        datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (purchase_return_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='进货退单明细';

# 调拨单
create table if not exists inventory_transfer
(
    id                  bigint unsigned auto_increment primary key                    not null comment 'ID',
    transfer_sn         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '调拨单号',
    pre_warehouse_id    bigint unsigned                                               not null default 0 comment '原仓库',
    pre_warehouse_name  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '原仓库名称',
    post_warehouse_id   bigint unsigned                                               not null default 0 comment '目标仓库',
    post_warehouse_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '目标仓库名称',
    total_amount        decimal(11, 2)                                                not null default 0.00 comment '调拨金额',
    status              tinyint unsigned                                              not null default 0 comment '状态:1=正常,2=作废',
    review_status       tinyint unsigned                                              not null default 0 comment '审核状态:0=未审核,1=审核通过',
    review_user_id      bigint unsigned                                               not null default 0 comment '审核人',
    review_user_name    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '审核人名称',
    review_time         datetime                                                      null comment '审核时间',
    outbound_status     tinyint unsigned                                              not null default 0 comment '出库状态:0=未出库,1=已出库,2=部分出库',
    inbound_status      tinyint unsigned                                              not null default 0 comment '入库状态:0=未入库,1=已入库,2=部分入库', # 部分入库来源于部分出库
    remark              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id           bigint unsigned                                               not null default 1 comment '租户',
    deleted             tinyint                                                       not null default 0 comment '软删除',
    create_time         datetime                                                               default current_timestamp comment '创建时间',
    update_time         datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (review_status),
    index (transfer_sn),
    index (pre_warehouse_id),
    index (post_warehouse_id),
    index (outbound_status, inbound_status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='调拨单';

# 调拨单明细
create table if not exists inventory_transfer_item
(
    id                    bigint unsigned auto_increment primary key                    not null comment 'ID',
    inventory_transfer_id bigint unsigned                                               not null default 0 comment '调拨单ID',
    goods_name            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品名称',
    goods_sku_id          bigint unsigned                                               not null default 0 comment '商品SKU',
    goods_sku_name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '商品SKU名称',
    quantity              decimal(11, 2)                                                not null default 0 comment '数量',
    unit_cost             decimal(11, 2)                                                not null default 0.00 comment '单位成本',
    amount                decimal(11, 2)                                                not null default 0.00 comment '金额',
    remark                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',
    tenant_id             bigint unsigned                                               not null default 1 comment '租户',
    deleted               tinyint                                                       not null default 0 comment '软删除',
    create_time           datetime                                                               default current_timestamp comment '创建时间',
    update_time           datetime                                                               default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted),
    index (inventory_transfer_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='调拨单明细';


# 盘点单
# 一次性提交，一个清点库存数量的活动，开始时锁定库存，盘点完成后计算出库存差异，解锁库存，然后生成出入库单、审核并执行出入库操作
create table if not exists inventory_count
(
    id                     bigint unsigned auto_increment primary key                   not null comment 'ID',
    count_sn               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '盘点单号',
    type                   tinyint unsigned                                             not null default 0 comment '类型:1=部分盘点,2=整库盘点',            #PD
    warehouse_id           bigint unsigned                                              not null default 0 comment '仓库ID',
    warehouse_name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '仓库名称',
    status                 tinyint unsigned                                             not null default 0 comment '状态:1=正常,2=作废',
    count_status           tinyint unsigned                                             not null default 0 comment '盘点结果:0=未盘点,1=盘点中,2=盘点完成', #影响库存锁定情况，状态修改依赖于两次审核
    count_start_time       datetime                                                              default null comment '盘点开始时间',
    count_end_time         datetime                                                              default null comment '盘点结束时间',
    inventory_status       tinyint unsigned                                             not null default 0 comment '库存状态:0=未调整,1=已调整,2=部分已调整',
    create_user_id         bigint unsigned                                              not null default 0 comment '创建人ID',
    create_user_name       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '创建人名称',
    count_date             datetime                                                              default null comment '退货日期',
    review_status          tinyint unsigned                                             not null default 0 comment '盘点前审核状态:0=待审核,1=已审核',      # 审核后盘点单才可以开始盘点，锁定库存，并且单据基本信息不允许修改
    review_user_id         bigint unsigned                                              not null default 0 comment '盘点前审核人ID',
    review_user_name       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '审核人名称',
    review_time            datetime                                                     null comment '审核时间',
    count_user_id          bigint unsigned                                              not null default 0 comment '盘点人ID',
    count_user_name        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '盘点人名称',
    count_review_status    tinyint unsigned                                             not null default 0 comment '盘点结果审核状态:0=待审核,1=已审核',    # 结果审核后可以执行盘点完成，释放库存锁，生成出入库单，执行出入库单
    count_review_user_id   bigint unsigned                                              not null default 0 comment '盘点结果审核人ID',
    count_review_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '盘点结果审核人名称',
    count_review_time      datetime                                                     null comment '盘点结果审核时间',
    tenant_id              bigint unsigned                                              not null default 1 comment '租户',
    deleted                tinyint                                                      not null default 0 comment '软删除',
    create_time            datetime                                                              default current_timestamp comment '创建时间',
    update_time            datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (tenant_id, deleted, status),
    index (count_sn),
    index (warehouse_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='库存盘点';

create table if not exists inventory_count_item
(
    id                 bigint unsigned auto_increment primary key                   not null comment 'ID',
    inventory_count_id bigint unsigned                                              not null default 0 comment '盘点单ID',
    goods_sku_id       bigint unsigned                                              not null default 0 comment '商品SKU',
    goods_name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品名称',
    goods_sku_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品SKU名称',
    pre_quantity       decimal(11, 2)                                               not null default 0.00 comment '盘前数量',
    pre_unit_cost      decimal(11, 2)                                               not null default 0.00 comment '盘前单位成本',
    pre_amount         decimal(11, 2)                                               not null default 0.00 comment '盘前总金额',
    post_quantity      decimal(11, 2)                                               not null default 0.00 comment '盘后数量', #
    post_unit_cost     decimal(11, 2)                                               not null default 0.00 comment '盘后单位成本',
    post_amount        decimal(11, 2)                                               not null default 0.00 comment '盘后总金额',
    quantity           decimal(11, 2)                                               not null default 0.00 comment '变动数量', # 生成出入库单的数量依据，根据库存锁定时机判断吧自动计算还是手动填写
    amount             decimal(11, 2)                                               not null default 0.00 comment '变动金额',
    tenant_id          bigint unsigned                                              not null default 1 comment '租户',
    deleted            tinyint                                                      not null default 0 comment '软删除',
    create_time        datetime                                                              default current_timestamp comment '创建时间',
    update_time        datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (deleted, tenant_id),
    index (inventory_count_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='盘点明细';



#  todo:暂时不开发
# 库存成本调整单
create table if not exists cost_adjustment
(
    id               bigint unsigned auto_increment primary key                   not null comment 'ID',
    warehouse_id     bigint unsigned                                              not null default 0 comment '仓库ID',
    warehouse_name   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '仓库名称',
    total_amount     decimal(11, 2)                                               not null default 0.00 comment '变动金额',

    create_user_id   bigint unsigned                                              not null default 0 comment '创建人ID',
    create_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '创建人名称',
    review_user_id   bigint unsigned                                              not null default 0 comment '审核人ID',
    review_user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '审核人名称',
    review_time      datetime                                                              default null comment '审核时间',
    status           tinyint                                                      not null default 0 comment '状态:1=正常,2=作废',

    tenant_id        bigint unsigned                                              not null default 1 comment '租户',
    deleted          tinyint                                                      not null default 0 comment '软删除',
    create_time      datetime                                                              default current_timestamp comment '创建时间',
    update_time      datetime                                                              default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='成本调整单';

# 成本调整单细节
create table if not exists cost_adjustment_item
(
    id                 bigint unsigned auto_increment primary key                   not null comment 'ID',
    cost_adjustment_id bigint unsigned                                              not null default 0 comment '成本调整单ID',
    goods_sku_id       bigint unsigned                                              not null default 0 comment '商品SKU',
    goods_name         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品名称',
    goods_sku_name     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '商品SKU名称',
    pre_unit_cost      decimal(11, 2)                                               not null default 0.00 comment '调整前单位成本',
    pre_amount         decimal(11, 2)                                               not null default 0.00 comment '调整前总金额',
    post_unit_cost     decimal(11, 2)                                               not null default 0.00 comment '调整后单位成本',
    post_amount        decimal(11, 2)                                               not null default 0.00 comment '调整后金额',
    quantity           decimal(11, 2)                                               not null default 0.00 comment '调整数量',
    amount             decimal(11, 2)                                               not null default 0.00 comment '变动金额',
    unit_cost          decimal(11, 2)                                               not null default 0.00 comment '变化成本',
    tenant_id          bigint unsigned                                              not null default 1 comment '租户',
    deleted            tinyint                                                      not null default 0 comment '软删除',
    create_time        datetime                                                              default current_timestamp comment '创建时间',
    update_time        datetime                                                              default null on update current_timestamp COMMENT '更新时间',
    index (deleted, tenant_id),
    index (cost_adjustment_id),
    index (goods_sku_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='成本调整明细';
