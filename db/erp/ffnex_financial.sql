# 财务模块
drop table if exists account;
create table if not exists account
(
    id              bigint unsigned auto_increment primary key                   not null comment '编号',
    name            varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '名称',
    type            tinyint unsigned                                             not null default 0 comment '类型:1=银行,2=现金,3:其他', # 1:银行2:现金3:其他
    account_name    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '户名',
    account_no      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '账号',
    account_bank    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '银行/平台',
    balance         decimal(11, 2)                                               not null default 0.00 comment '余额',
    initial_balance decimal(11, 2)                                               not null default 0.00 comment '期初余额',

    status          tinyint                                                      not null default 1 comment '状态:1=启用,2=停用',        # 1:启用 2:停用
    weigh           int unsigned                                                 not null default 0 comment '排序',
    tenant_id       int unsigned                                                 not null default 1 comment '租户',
    deleted         tinyint                                                      not null default 0 comment '软删除',
    create_time     datetime                                                              default current_timestamp comment '创建时间',
    update_time     datetime                                                              default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  auto_increment = 10001
  ROW_FORMAT = DYNAMIC COMMENT ='账户';

# 账户流水
create table if not exists account_log
(
    id            bigint unsigned auto_increment primary key                    not null comment 'ID',
    account_id    bigint unsigned                                               not null default 0 comment '账户',
    business_id   bigint unsigned                                               not null default 0 comment '业务ID',
    business_type tinyint                                                       not null default 0 comment '业务类型', # 1:收款-1:付款
    business_sn   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '业务编号',
    pre_balance   decimal(11, 2)                                                not null default 0.00 comment '变动前余额',
    amount        decimal(11, 2)                                                not null default 0.00 comment '金额',  #可以是负值
    post_balance  decimal(11, 2)                                                not null default 0.00 comment '变动后余额',
    remark        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',

    status        tinyint                                                       not null default 1 comment '状态',     # 1:启用 2:停用
    weigh         int unsigned                                                  not null default 0 comment '排序',
    tenant_id     int unsigned                                                  not null default 1 comment '租户',
    deleted       tinyint                                                       not null default 0 comment '软删除',
    create_time   datetime                                                               default current_timestamp comment '发生时间',
    update_time   datetime                                                               default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='账户流水';

# 收款单
create table if not exists receipt_bill
(
    id            bigint unsigned auto_increment primary key                    not null comment 'ID',
    bill_sn       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '收款单编号', # SK
    business_id   bigint unsigned                                               not null default 0 comment '业务ID',
    business_type tinyint                                                       not null default 0 comment '业务类型',    # 1=销售，2=进货退款，3=转入 ,4-对账盈余
    business_sn   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '业务编号',
    amount        decimal(11, 2)                                                not null default 0.00 comment '收款金额',
    account_id    bigint unsigned                                               not null default 0 comment '收款账户',
    remark        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',

    tenant_id     int unsigned                                                  not null default 1 comment '租户',
    deleted       tinyint                                                       not null default 0 comment '软删除',
    create_time   datetime                                                               default current_timestamp comment '发生时间',
    update_time   datetime                                                               default null on update current_timestamp COMMENT '更新时间'

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='收款单';

# 付款单
create table if not exists payment_bill
(
    id            bigint unsigned auto_increment primary key                    not null comment 'ID',
    bill_sn       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '付款单编号', # FK
    business_id   bigint unsigned                                               not null default 0 comment '业务ID',
    business_type tinyint                                                       not null default 0 comment '业务类型',    # 1=进货，2=销售退款，3=转出 ，4-对账亏损
    business_sn   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  not null default '' comment '业务编号',
    account_id    bigint unsigned                                               not null default 0 comment '付款账户',
    remark        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci not null default '' comment '备注',

    tenant_id     int unsigned                                                  not null default 1 comment '租户',
    deleted       tinyint                                                       not null default 0 comment '软删除',
    create_time   datetime                                                               default current_timestamp comment '发生时间',
    update_time   datetime                                                               default null on update current_timestamp COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='付款单';