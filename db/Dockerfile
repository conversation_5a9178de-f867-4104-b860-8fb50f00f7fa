FROM registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/mysql-server:8.0.32

MAINTAINER youxuan(<EMAIL>)

ENV TZ=Asia/Shanghai
ENV LANG C.UTF-8

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./1schema.sql /docker-entrypoint-initdb.d

COPY ./2youxuanx.sql /docker-entrypoint-initdb.d

COPY ./3youxuanx_flow.sql /docker-entrypoint-initdb.d

COPY ./4youxuanx_job.sql /docker-entrypoint-initdb.d

COPY ./5youxuanx_mp.sql /docker-entrypoint-initdb.d

COPY ./6youxuanx_config.sql /docker-entrypoint-initdb.d

COPY ./7youxuanx_pay.sql /docker-entrypoint-initdb.d

COPY ./8youxuanx_codegen.sql /docker-entrypoint-initdb.d

COPY ./99youxuanx_bi.sql /docker-entrypoint-initdb.d

COPY ./999youxuanx_app.sql /docker-entrypoint-initdb.d
