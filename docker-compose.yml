# 使用说明 V5.2
# 1. 使用docker-compose  宿主机不需要配置host来发现
# 2. 无需修改源码，根目录  docker-compose up 即可
# 3. 静静等待服务启动

version: '3'
services:
  youxuan-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_HOST: "%"
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: youxuan-mysql
    image: youxuan-mysql
    command: --lower_case_table_names=1
    networks:
      - spring_cloud_default

  youxuan-redis:
    container_name: youxuan-redis
    image: registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/redis
    restart: always
    networks:
      - spring_cloud_default

  youxuan-register:
    build:
      context: ./youxuan-register
    restart: always
    container_name: youxuan-register
    image: youxuan-register
    ports:
      - 8848:8848
    networks:
      - spring_cloud_default

  youxuan-gateway:
    build:
      context: ./youxuan-gateway
    restart: always
    container_name: youxuan-gateway
    image: youxuan-gateway
    ports:
      - 9999:9999
    networks:
      - spring_cloud_default

  youxuan-auth:
    build:
      context: ./youxuan-auth
    restart: always
    container_name: youxuan-auth
    image: youxuan-auth
    networks:
      - spring_cloud_default

  youxuan-upms:
    build:
      context: ./youxuan-upms/youxuan-upms-biz
    restart: always
    container_name: youxuan-upms
    image: youxuan-upms
    networks:
      - spring_cloud_default

  youxuan-flow-task:
    build:
      context: ./youxuan-flow/youxuan-flow-task/youxuan-flow-task-biz
    restart: always
    container_name: youxuan-flow-task
    image: youxuan-flow-task
    networks:
      - spring_cloud_default

  youxuan-flow-engine:
    build:
      context: ./youxuan-flow/youxuan-flow-engine/youxuan-flow-engine-biz
    restart: always
    container_name: youxuan-flow-engine
    image: youxuan-flow-engine
    networks:
      - spring_cloud_default

  youxuan-app-server:
    build:
      context: ./youxuan-app-server/youxuan-app-server-biz
    restart: always
    container_name: youxuan-app-server
    image: youxuan-app-server
    networks:
      - spring_cloud_default

  youxuan-monitor:
    build:
      context: ./youxuan-visual/youxuan-monitor
    restart: always
    image: youxuan-monitor
    container_name: youxuan-monitor
    ports:
      - 5001:5001
    networks:
      - spring_cloud_default

  youxuan-daemon-quartz:
    build:
      context: ./youxuan-visual/youxuan-daemon-quartz
    restart: always
    image: youxuan-daemon-quartz
    container_name: youxuan-daemon-quartz
    networks:
      - spring_cloud_default

  youxuan-daemon-elastic-job:
    build:
      context: ./youxuan-visual/youxuan-daemon-elastic-job
    restart: always
    image: youxuan-daemon-elastic-job
    container_name: youxuan-daemon-elastic-job
    networks:
      - spring_cloud_default

  youxuan-codegen:
    build:
      context: ./youxuan-visual/youxuan-codegen
    restart: always
    image: youxuan-codegen
    container_name: youxuan-codegen
    networks:
      - spring_cloud_default

  youxuan-mp-platform:
    build:
      context: ./youxuan-visual/youxuan-mp-platform
    restart: always
    image: youxuan-mp-platform
    container_name: youxuan-mp-platform
    networks:
      - spring_cloud_default

  youxuan-pay-platform:
    build:
      context: ./youxuan-visual/youxuan-pay-platform
    restart: always
    image: youxuan-pay-platform
    container_name: youxuan-pay-platform
    networks:
      - spring_cloud_default

  youxuan-report-platform:
    build:
      context: ./youxuan-visual/youxuan-report-platform
    restart: always
    image: youxuan-report-platform
    container_name: youxuan-report-platform
    ports:
      - 9095:9095
    networks:
      - spring_cloud_default

  youxuan-jimu-platform:
    build:
      context: ./youxuan-visual/youxuan-jimu-platform
    restart: always
    image: youxuan-jimu-platform
    container_name: youxuan-jimu-platform
    ports:
      - 5008:5008
    networks:
      - spring_cloud_default

networks:
  spring_cloud_default:
    name:  spring_cloud_default
    driver: bridge
